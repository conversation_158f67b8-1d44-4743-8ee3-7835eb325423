#!/bin/bash

# RGW 动态限速配置示例脚本
# 该脚本演示如何配置和使用 RGW 的动态限速功能

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 ceph 命令是否可用
check_ceph_available() {
    if ! command -v ceph &> /dev/null; then
        log_error "ceph 命令未找到，请确保 Ceph 已正确安装并配置"
        exit 1
    fi
    
    if ! ceph status &> /dev/null; then
        log_error "无法连接到 Ceph 集群，请检查配置"
        exit 1
    fi
    
    log_success "Ceph 集群连接正常"
}

# 显示当前配置
show_current_config() {
    log_info "当前 RGW 动态限速配置："
    echo "----------------------------------------"

    # 主开关
    local datalog_throttle=$(ceph config get client.rgw rgw_datalog_trim_dynamic_throttle 2>/dev/null || echo "false")
    local bilog_throttle=$(ceph config get client.rgw rgw_bilog_trim_dynamic_throttle 2>/dev/null || echo "false")
    echo "DataLog 动态限速开关: $datalog_throttle"
    echo "Bucket Index Log 动态限速开关: $bilog_throttle"
    
    # DataLog CPU 阈值
    local datalog_cpu_low=$(ceph config get client.rgw rgw_datalog_trim_cpu_threshold_low 2>/dev/null || echo "0.5")
    local datalog_cpu_high=$(ceph config get client.rgw rgw_datalog_trim_cpu_threshold_high 2>/dev/null || echo "0.8")
    echo "DataLog CPU 负载阈值: 低=$datalog_cpu_low, 高=$datalog_cpu_high"

    # DataLog 内存阈值
    local datalog_mem_low=$(ceph config get client.rgw rgw_datalog_trim_mem_threshold_low 2>/dev/null || echo "0.6")
    local datalog_mem_high=$(ceph config get client.rgw rgw_datalog_trim_mem_threshold_high 2>/dev/null || echo "0.85")
    echo "DataLog 内存使用阈值: 低=$datalog_mem_low, 高=$datalog_mem_high"

    # Bucket Index Log CPU 阈值
    local bilog_cpu_low=$(ceph config get client.rgw rgw_bilog_trim_cpu_threshold_low 2>/dev/null || echo "0.5")
    local bilog_cpu_high=$(ceph config get client.rgw rgw_bilog_trim_cpu_threshold_high 2>/dev/null || echo "0.8")
    echo "Bucket Index Log CPU 负载阈值: 低=$bilog_cpu_low, 高=$bilog_cpu_high"

    # Bucket Index Log 内存阈值
    local bilog_mem_low=$(ceph config get client.rgw rgw_bilog_trim_mem_threshold_low 2>/dev/null || echo "0.6")
    local bilog_mem_high=$(ceph config get client.rgw rgw_bilog_trim_mem_threshold_high 2>/dev/null || echo "0.85")
    echo "Bucket Index Log 内存使用阈值: 低=$bilog_mem_low, 高=$bilog_mem_high"
    
    # 休眠时间
    local sleep_low=$(ceph config get client.rgw rgw_datalog_trim_sleep_low 2>/dev/null || echo "10")
    local sleep_medium=$(ceph config get client.rgw rgw_datalog_trim_sleep_medium 2>/dev/null || echo "50")
    local sleep_high=$(ceph config get client.rgw rgw_datalog_trim_sleep_high 2>/dev/null || echo "200")
    echo "休眠时间(ms): 低=$sleep_low, 中=$sleep_medium, 高=$sleep_high"
    
    # 批次大小
    local batch_low=$(ceph config get client.rgw rgw_datalog_trim_batch_size_low 2>/dev/null || echo "1000")
    local batch_medium=$(ceph config get client.rgw rgw_datalog_trim_batch_size_medium 2>/dev/null || echo "500")
    local batch_high=$(ceph config get client.rgw rgw_datalog_trim_batch_size_high 2>/dev/null || echo "100")
    echo "批次大小: 低=$batch_low, 中=$batch_medium, 高=$batch_high"
    
    echo "----------------------------------------"
}

# 启用动态限速
enable_dynamic_throttle() {
    log_info "启用 RGW 动态限速..."

    ceph config set client.rgw rgw_datalog_trim_dynamic_throttle true
    ceph config set client.rgw rgw_bilog_trim_dynamic_throttle true

    log_success "DataLog 和 Bucket Index Log 动态限速已启用"
}

# 禁用动态限速
disable_dynamic_throttle() {
    log_info "禁用 RGW 动态限速..."

    ceph config set client.rgw rgw_datalog_trim_dynamic_throttle false
    ceph config set client.rgw rgw_bilog_trim_dynamic_throttle false

    log_success "DataLog 和 Bucket Index Log 动态限速已禁用"
}

# 配置高性能场景
configure_high_performance() {
    log_info "配置高性能场景参数..."
    
    # 提高负载阈值，允许更高的负载
    ceph config set client.rgw rgw_datalog_trim_cpu_threshold_low 0.7
    ceph config set client.rgw rgw_datalog_trim_cpu_threshold_high 0.9
    ceph config set client.rgw rgw_datalog_trim_mem_threshold_low 0.7
    ceph config set client.rgw rgw_datalog_trim_mem_threshold_high 0.9
    
    # 减少休眠时间，提高处理速度
    ceph config set client.rgw rgw_datalog_trim_sleep_low 5
    ceph config set client.rgw rgw_datalog_trim_sleep_medium 25
    ceph config set client.rgw rgw_datalog_trim_sleep_high 100
    
    # 增加批次大小，提高效率
    ceph config set client.rgw rgw_datalog_trim_batch_size_low 1500
    ceph config set client.rgw rgw_datalog_trim_batch_size_medium 750
    ceph config set client.rgw rgw_datalog_trim_batch_size_high 200
    
    log_success "高性能场景配置完成"
}

# 配置资源受限场景
configure_resource_limited() {
    log_info "配置资源受限场景参数..."
    
    # 降低负载阈值，更早触发限速
    ceph config set client.rgw rgw_datalog_trim_cpu_threshold_low 0.3
    ceph config set client.rgw rgw_datalog_trim_cpu_threshold_high 0.6
    ceph config set client.rgw rgw_datalog_trim_mem_threshold_low 0.5
    ceph config set client.rgw rgw_datalog_trim_mem_threshold_high 0.75
    
    # 增加休眠时间，减少系统压力
    ceph config set client.rgw rgw_datalog_trim_sleep_low 20
    ceph config set client.rgw rgw_datalog_trim_sleep_medium 100
    ceph config set client.rgw rgw_datalog_trim_sleep_high 500
    
    # 减少批次大小，降低资源使用
    ceph config set client.rgw rgw_datalog_trim_batch_size_low 500
    ceph config set client.rgw rgw_datalog_trim_batch_size_medium 250
    ceph config set client.rgw rgw_datalog_trim_batch_size_high 50
    
    log_success "资源受限场景配置完成"
}

# 配置延迟敏感场景
configure_latency_sensitive() {
    log_info "配置延迟敏感场景参数..."
    
    # 中等负载阈值，平衡性能和延迟
    ceph config set client.rgw rgw_datalog_trim_cpu_threshold_low 0.4
    ceph config set client.rgw rgw_datalog_trim_cpu_threshold_high 0.7
    ceph config set client.rgw rgw_datalog_trim_mem_threshold_low 0.55
    ceph config set client.rgw rgw_datalog_trim_mem_threshold_high 0.8
    
    # 激进的限速策略，优先保证延迟
    ceph config set client.rgw rgw_datalog_trim_sleep_low 15
    ceph config set client.rgw rgw_datalog_trim_sleep_medium 75
    ceph config set client.rgw rgw_datalog_trim_sleep_high 300
    
    # 较小的批次大小，减少单次操作影响
    ceph config set client.rgw rgw_datalog_trim_batch_size_low 800
    ceph config set client.rgw rgw_datalog_trim_batch_size_medium 300
    ceph config set client.rgw rgw_datalog_trim_batch_size_high 75
    
    log_success "延迟敏感场景配置完成"
}

# 恢复默认配置
restore_defaults() {
    log_info "恢复默认配置..."
    
    # 恢复所有配置到默认值
    ceph config rm client.rgw rgw_datalog_trim_dynamic_throttle 2>/dev/null || true
    ceph config rm client.rgw rgw_datalog_trim_cpu_threshold_low 2>/dev/null || true
    ceph config rm client.rgw rgw_datalog_trim_cpu_threshold_high 2>/dev/null || true
    ceph config rm client.rgw rgw_datalog_trim_mem_threshold_low 2>/dev/null || true
    ceph config rm client.rgw rgw_datalog_trim_mem_threshold_high 2>/dev/null || true
    ceph config rm client.rgw rgw_datalog_trim_sleep_low 2>/dev/null || true
    ceph config rm client.rgw rgw_datalog_trim_sleep_medium 2>/dev/null || true
    ceph config rm client.rgw rgw_datalog_trim_sleep_high 2>/dev/null || true
    ceph config rm client.rgw rgw_datalog_trim_batch_size_low 2>/dev/null || true
    ceph config rm client.rgw rgw_datalog_trim_batch_size_medium 2>/dev/null || true
    ceph config rm client.rgw rgw_datalog_trim_batch_size_high 2>/dev/null || true
    
    log_success "默认配置已恢复"
}

# 监控限速效果
monitor_throttling() {
    log_info "监控动态限速效果..."
    log_info "按 Ctrl+C 停止监控"
    
    # 检查 RGW 日志文件
    local rgw_log_files=$(find /var/log/ceph -name "ceph-client.rgw.*.log" 2>/dev/null | head -1)
    
    if [ -z "$rgw_log_files" ]; then
        log_warning "未找到 RGW 日志文件，请检查日志路径"
        return 1
    fi
    
    echo "监控日志文件: $rgw_log_files"
    echo "----------------------------------------"
    
    # 实时监控相关日志
    tail -f "$rgw_log_files" | grep --line-buffered -E "(RGWDataLogLoadMonitor|trim_entries_throttled|throttling after)" || true
}

# 显示帮助信息
show_help() {
    echo "RGW 动态限速配置工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  show                    显示当前配置"
    echo "  enable                  启用动态限速"
    echo "  disable                 禁用动态限速"
    echo "  high-performance        配置高性能场景"
    echo "  resource-limited        配置资源受限场景"
    echo "  latency-sensitive       配置延迟敏感场景"
    echo "  restore-defaults        恢复默认配置"
    echo "  monitor                 监控限速效果"
    echo "  help                    显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 show                 # 查看当前配置"
    echo "  $0 enable               # 启用动态限速"
    echo "  $0 high-performance     # 配置高性能场景"
    echo "  $0 monitor              # 监控限速效果"
}

# 主函数
main() {
    case "${1:-help}" in
        "show")
            check_ceph_available
            show_current_config
            ;;
        "enable")
            check_ceph_available
            enable_dynamic_throttle
            show_current_config
            ;;
        "disable")
            check_ceph_available
            disable_dynamic_throttle
            show_current_config
            ;;
        "high-performance")
            check_ceph_available
            enable_dynamic_throttle
            configure_high_performance
            show_current_config
            ;;
        "resource-limited")
            check_ceph_available
            enable_dynamic_throttle
            configure_resource_limited
            show_current_config
            ;;
        "latency-sensitive")
            check_ceph_available
            enable_dynamic_throttle
            configure_latency_sensitive
            show_current_config
            ;;
        "restore-defaults")
            check_ceph_available
            restore_defaults
            show_current_config
            ;;
        "monitor")
            check_ceph_available
            monitor_throttling
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
