// -*- mode:C++; tab-width:8; c-basic-offset:2; indent-tabs-mode:t -*-
// vim: ts=8 sw=2 smarttab ft=cpp

#include <gtest/gtest.h>
#include <memory>
#include <chrono>
#include <thread>

#include "common/ceph_context.h"
#include "common/config.h"
#include "rgw/rgw_datalog.cc"  // Include implementation for testing

class TestRGWDataLogLoadMonitor : public ::testing::Test {
protected:
  std::unique_ptr<CephContext> cct;
  std::unique_ptr<RGWDataLogLoadMonitor> load_monitor;

  void SetUp() override {
    // Create a minimal CephContext for testing
    std::vector<const char*> args;
    auto code = global_init(nullptr, args, CEPH_ENTITY_TYPE_CLIENT,
                           CODE_ENVIRONMENT_UTILITY, 0);
    ASSERT_EQ(0, code);
    
    cct = std::make_unique<CephContext>(CEPH_ENTITY_TYPE_CLIENT);
    load_monitor = std::make_unique<RGWDataLogLoadMonitor>(cct.get());
  }

  void TearDown() override {
    load_monitor.reset();
    cct.reset();
  }
};

TEST_F(TestRGWDataLogLoadMonitor, BasicLoadMetrics) {
  // Test basic load metric collection
  load_monitor->update_load_metrics();
  
  // Should not crash and should return reasonable values
  auto load_level = load_monitor->get_current_load_level();
  EXPECT_GE(static_cast<int>(load_level), 0);
  EXPECT_LE(static_cast<int>(load_level), 2);
  
  auto sleep_time = load_monitor->get_sleep_time_ms();
  EXPECT_GT(sleep_time, 0);
  EXPECT_LT(sleep_time, 10000); // Should be reasonable
  
  auto batch_size = load_monitor->get_batch_size();
  EXPECT_GT(batch_size, 0);
  EXPECT_LT(batch_size, 10000); // Should be reasonable
}

TEST_F(TestRGWDataLogLoadMonitor, LoadLevelProgression) {
  // Test that load levels progress logically
  load_monitor->update_load_metrics();
  
  auto low_sleep = load_monitor->config.sleep_ms_low;
  auto medium_sleep = load_monitor->config.sleep_ms_medium;
  auto high_sleep = load_monitor->config.sleep_ms_high;
  
  // Sleep times should increase with load level
  EXPECT_LT(low_sleep, medium_sleep);
  EXPECT_LT(medium_sleep, high_sleep);
  
  auto low_batch = load_monitor->config.batch_size_low;
  auto medium_batch = load_monitor->config.batch_size_medium;
  auto high_batch = load_monitor->config.batch_size_high;
  
  // Batch sizes should decrease with load level
  EXPECT_GT(low_batch, medium_batch);
  EXPECT_GT(medium_batch, high_batch);
}

TEST_F(TestRGWDataLogLoadMonitor, ThrottleDecision) {
  // Test throttle decision logic
  load_monitor->update_load_metrics();
  
  auto should_throttle = load_monitor->should_throttle();
  auto load_level = load_monitor->get_current_load_level();
  
  if (load_level == RGWDataLogLoadMonitor::LoadLevel::LOW) {
    EXPECT_FALSE(should_throttle);
  } else {
    EXPECT_TRUE(should_throttle);
  }
}

TEST_F(TestRGWDataLogLoadMonitor, ConfigurationValues) {
  // Test that configuration values are within expected ranges
  EXPECT_GT(load_monitor->config.cpu_threshold_low, 0.0);
  EXPECT_LT(load_monitor->config.cpu_threshold_low, 1.0);
  EXPECT_GT(load_monitor->config.cpu_threshold_high, load_monitor->config.cpu_threshold_low);
  
  EXPECT_GT(load_monitor->config.mem_threshold_low, 0.0);
  EXPECT_LT(load_monitor->config.mem_threshold_low, 1.0);
  EXPECT_GT(load_monitor->config.mem_threshold_high, load_monitor->config.mem_threshold_low);
  
  EXPECT_GT(load_monitor->config.sleep_ms_low, 0);
  EXPECT_GT(load_monitor->config.sleep_ms_medium, load_monitor->config.sleep_ms_low);
  EXPECT_GT(load_monitor->config.sleep_ms_high, load_monitor->config.sleep_ms_medium);
}

TEST_F(TestRGWDataLogLoadMonitor, MetricsUpdate) {
  // Test that metrics update doesn't crash and updates timestamp
  auto before = std::chrono::steady_clock::now();
  load_monitor->update_load_metrics();
  auto after = std::chrono::steady_clock::now();
  
  // Should complete quickly
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(after - before);
  EXPECT_LT(duration.count(), 1000); // Should complete within 1 second
}

// Mock test for trim operations with throttling
class MockDataLogBackends {
public:
  int trim_operations_count = 0;
  std::vector<uint32_t> sleep_times;
  
  int trim_entries_throttled(const DoutPrefixProvider *dpp,
                           int shard_id,
                           std::string_view marker,
                           RGWDataLogLoadMonitor* load_monitor) {
    if (!load_monitor) {
      return 0;
    }
    
    load_monitor->update_load_metrics();
    
    const uint32_t batch_size = load_monitor->get_batch_size();
    const uint32_t sleep_time_ms = load_monitor->get_sleep_time_ms();
    
    // Simulate trim operations
    for (int i = 0; i < 100; ++i) {
      trim_operations_count++;
      
      if (load_monitor->should_throttle() && 
          trim_operations_count % batch_size == 0) {
        sleep_times.push_back(sleep_time_ms);
        // Don't actually sleep in test
      }
    }
    
    return 0;
  }
};

TEST_F(TestRGWDataLogLoadMonitor, ThrottledTrimBehavior) {
  MockDataLogBackends mock_backends;
  
  // Test throttled trim behavior
  int result = mock_backends.trim_entries_throttled(
    nullptr, 0, "test_marker", load_monitor.get());
  
  EXPECT_EQ(result, 0);
  EXPECT_EQ(mock_backends.trim_operations_count, 100);
  
  // Should have applied throttling if load is not low
  auto load_level = load_monitor->get_current_load_level();
  if (load_level != RGWDataLogLoadMonitor::LoadLevel::LOW) {
    EXPECT_GT(mock_backends.sleep_times.size(), 0);
  }
}

// Integration test for configuration loading
TEST_F(TestRGWDataLogLoadMonitor, ConfigurationIntegration) {
  // Test that configuration can be loaded from ceph context
  // This tests the integration with the configuration system
  
  // Set some test values
  cct->_conf.set_val("rgw_datalog_trim_cpu_threshold_low", "0.3");
  cct->_conf.set_val("rgw_datalog_trim_cpu_threshold_high", "0.7");
  cct->_conf.set_val("rgw_datalog_trim_sleep_low", "5");
  cct->_conf.set_val("rgw_datalog_trim_sleep_high", "100");
  
  // Create new monitor with updated config
  auto test_monitor = std::make_unique<RGWDataLogLoadMonitor>(cct.get());
  
  // Verify configuration was loaded
  EXPECT_DOUBLE_EQ(test_monitor->config.cpu_threshold_low, 0.3);
  EXPECT_DOUBLE_EQ(test_monitor->config.cpu_threshold_high, 0.7);
  EXPECT_EQ(test_monitor->config.sleep_ms_low, 5);
  EXPECT_EQ(test_monitor->config.sleep_ms_high, 100);
}

int main(int argc, char **argv) {
  ::testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}
