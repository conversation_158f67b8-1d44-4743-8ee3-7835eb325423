# -*- mode: YAML -*-
---

options:
# According to AWS S3(http://docs.aws.amazon.com/AmazonS3/latest/dev/acl-overview.html),
# An ACL can have up to 100 grants.
- name: rgw_acl_grants_max_num
  type: int
  level: advanced
  desc: Max number of ACL grants in a single request
  default: 100
  services:
  - rgw
  with_legacy: true
# According to AWS S3(http://docs.aws.amazon.com/AmazonS3/latest/dev/cors.html),
# An cors can have up to 100 rules.
- name: rgw_cors_rules_max_num
  type: int
  level: advanced
  desc: Max number of cors rules in a single request
  default: 100
  services:
  - rgw
  with_legacy: true
# According to AWS S3(https://docs.aws.amazon.com/AmazonS3/latest/dev/DeletingObjects.html),
# Amazon S3 also provides the Multi-Object Delete API that you can use to delete up to 1000
# objects in a single HTTP request.
- name: rgw_delete_multi_obj_max_num
  type: int
  level: advanced
  desc: Max number of objects in a single multi-object delete request
  default: 1000
  services:
  - rgw
  with_legacy: true
# According to AWS S3, An website routing config can have up to 50 rules.
- name: rgw_website_routing_rules_max_num
  type: int
  level: advanced
  desc: Max number of website routing rules in a single request
  default: 50
  services:
  - rgw
  with_legacy: true
- name: rgw_rados_tracing
  type: bool
  level: advanced
  desc: true if LTTng-UST tracepoints should be enabled
  default: false
  services:
  - rgw
- name: rgw_op_tracing
  type: bool
  level: advanced
  desc: true if LTTng-UST tracepoints should be enabled
  default: false
  services:
  - rgw
- name: rgw_max_chunk_size
  type: size
  level: advanced
  desc: Set RGW max chunk size
  long_desc: The chunk size is the size of RADOS I/O requests that RGW sends when
    accessing data objects. RGW read and write operations will never request more than
    this amount in a single request. This also defines the RGW head object size, as
    head operations need to be atomic, and anything larger than this would require
    more than a single operation. When RGW objects are written to the default
    storage class, up to this amount of payload data will be stored alongside
    metadata in the head object.
  default: 4_M
  services:
  - rgw
  with_legacy: true
- name: rgw_put_obj_min_window_size
  type: size
  level: advanced
  desc: The minimum RADOS write window size (in bytes).
  long_desc: The window size determines the total concurrent RADOS writes of a single
    RGW object. When writing an object RGW will send multiple chunks to RADOS. The
    total size of the writes does not exceed the window size. The window size may
    be adjusted dynamically in order to better utilize the pipe.
  default: 16_M
  services:
  - rgw
  see_also:
  - rgw_put_obj_max_window_size
  - rgw_max_chunk_size
  with_legacy: true
- name: rgw_put_obj_max_window_size
  type: size
  level: advanced
  desc: The maximum RADOS write window size (in bytes).
  long_desc: The window size may be dynamically adjusted, but will not surpass this
    value.
  default: 64_M
  services:
  - rgw
  see_also:
  - rgw_put_obj_min_window_size
  - rgw_max_chunk_size
  with_legacy: true
- name: rgw_max_put_size
  type: size
  level: advanced
  desc: Max size (in bytes) of regular (non multi-part) object upload.
  long_desc: Plain object upload is capped at this amount of data. In order to upload
    larger objects, a special upload mechanism is required. The S3 API provides the
    multi-part upload, and Swift provides DLO and SLO.
  default: 5_G
  services:
  - rgw
  with_legacy: true
- name: rgw_max_put_param_size
  type: size
  level: advanced
  desc: The maximum size (in bytes) of data input of certain RESTful requests.
  default: 1_M
  services:
  - rgw
  with_legacy: true
- name: rgw_max_attr_size
  type: size
  level: advanced
  desc: The maximum length of metadata value. 0 skips the check
  default: 0
  services:
  - rgw
  with_legacy: true
- name: rgw_max_attr_name_len
  type: size
  level: advanced
  desc: The maximum length of metadata name. 0 skips the check
  default: 0
  services:
  - rgw
  with_legacy: true
- name: rgw_max_attrs_num_in_req
  type: uint
  level: advanced
  desc: The maximum number of metadata items that can be put via single request
  default: 0
  services:
  - rgw
  with_legacy: true
# override max bucket index shards in zone configuration (if not zero)
#
# Represents the number of shards for the bucket index object, a value of zero
# indicates there is no sharding. By default (no sharding, the name of the object
# is '.dir.{marker}', with sharding, the name is '.dir.{markder}.{sharding_id}',
# sharding_id is zero-based value. It is not recommended to set a too large value
# (e.g. thousand) as it increases the cost for bucket listing.
- name: rgw_override_bucket_index_max_shards
  type: uint
  level: dev
  desc: The default number of bucket index shards for newly-created buckets. This
    value overrides bucket_index_max_shards stored in the zone. Setting this value
    in the zone is preferred, because it applies globally to all radosgw daemons running
    in the zone.
  fmt_desc: Represents the number of shards for the bucket index object,
    a value of zero indicates there is no sharding. It is not
    recommended to set a value too large (e.g. thousand) as it
    increases the cost for bucket listing.
    This variable should be set in the client or global sections
    so that it is automatically applied to radosgw-admin commands.
  default: 0
  services:
  - rgw
  with_legacy: true
# Represents the maximum AIO pending requests for the bucket index object shards.
- name: rgw_bucket_index_max_aio
  type: uint
  level: advanced
  desc: Max number of concurrent RADOS requests when handling bucket shards.
  default: 128
  services:
  - rgw
  with_legacy: true
# whether or not the quota/gc threads should be started
- name: rgw_enable_quota_threads
  type: bool
  level: advanced
  desc: Enables the quota maintenance thread.
  long_desc: The quota maintenance thread is responsible for quota related maintenance
    work. The thread itself can be disabled, but in order for quota to work correctly,
    at least one RGW in each zone needs to have this thread running. Having the thread
    enabled on multiple RGW processes within the same zone can spread some of the
    maintenance work between them.
  default: true
  services:
  - rgw
  see_also:
  - rgw_enable_gc_threads
  - rgw_enable_lc_threads
  with_legacy: true
- name: rgw_enable_gc_threads
  type: bool
  level: advanced
  desc: Enables the garbage collection maintenance thread.
  long_desc: The garbage collection maintenance thread is responsible for garbage
    collector maintenance work. The thread itself can be disabled, but in order for
    garbage collection to work correctly, at least one RGW in each zone needs to have
    this thread running.  Having the thread enabled on multiple RGW processes within
    the same zone can spread some of the maintenance work between them.
  default: true
  services:
  - rgw
  see_also:
  - rgw_enable_quota_threads
  - rgw_enable_lc_threads
  with_legacy: true
- name: rgw_enable_lc_threads
  type: bool
  level: advanced
  desc: Enables the lifecycle maintenance thread. This is required on at least one
    rgw for each zone.
  long_desc: The lifecycle maintenance thread is responsible for lifecycle related
    maintenance work. The thread itself can be disabled, but in order for lifecycle
    to work correctly, at least one RGW in each zone needs to have this thread running.
    Havingthe thread enabled on multiple RGW processes within the same zone can spread
    some of the maintenance work between them.
  default: true
  services:
  - rgw
  see_also:
  - rgw_enable_gc_threads
  - rgw_enable_quota_threads
  with_legacy: true
- name: rgw_data
  type: str
  level: advanced
  desc: Alternative location for RGW configuration.
  long_desc: If this is set, the different Ceph system configurables (such as the keyring file will be located in the path that is specified here.
  fmt_desc: Sets the location of the data files for Ceph RADOS Gateway.
  default: /var/lib/ceph/radosgw/$cluster-$id
  services:
  - rgw
  flags:
  - no_mon_update
  with_legacy: true
- name: rgw_enable_apis
  type: str
  level: advanced
  desc: A list of set of RESTful APIs that rgw handles.
  fmt_desc: |
    Enables the specified APIs.

      .. note:: Enabling the ``s3`` API is a requirement for
                any ``radosgw`` instance that is meant to
                participate in a `multi-site <../multisite>`_
                configuration.
  default: s3, s3website, swift, swift_auth, admin, sts, iam, notifications
  services:
  - rgw
  with_legacy: true
- name: rgw_cache_enabled
  type: bool
  level: advanced
  desc: Enable RGW metadata cache.
  long_desc: The metadata cache holds metadata entries that RGW requires for processing
    requests. Metadata entries can be user info, bucket info, and bucket instance
    info. If not found in the cache, entries will be fetched from the backing RADOS
    store.
  fmt_desc: Whether the Ceph Object Gateway cache is enabled.
  default: true
  services:
  - rgw
  see_also:
  - rgw_cache_lru_size
  with_legacy: true
- name: rgw_cache_lru_size
  type: int
  level: advanced
  desc: Max number of items in RGW metadata cache.
  long_desc: When full, the RGW metadata cache evicts least recently used entries.
  fmt_desc: The number of entries in the Ceph Object Gateway cache.
  default: 10000
  services:
  - rgw
  see_also:
  - rgw_cache_enabled
  with_legacy: true
- name: rgw_dns_name
  type: str
  level: advanced
  desc: The host name that RGW uses.
  long_desc: This is Needed for virtual hosting of buckets to work properly, unless
    configured via zonegroup configuration.
  fmt_desc: The DNS name of the served domain. See also the ``hostnames`` setting within regions.
  services:
  - rgw
  with_legacy: true
- name: rgw_dns_s3website_name
  type: str
  level: advanced
  desc: The host name that RGW uses for static websites (S3)
  long_desc: This is needed for virtual hosting of buckets, unless configured via
    zonegroup configuration.
  services:
  - rgw
  with_legacy: true
- name: rgw_numa_node
  type: int
  level: advanced
  desc: set rgw's cpu affinity to a numa node (-1 for none)
  default: -1
  services:
  - rgw
  flags:
  - startup
- name: rgw_service_provider_name
  type: str
  level: advanced
  desc: Service provider name which is contained in http response headers
  long_desc: As S3 or other cloud storage providers do, http response headers should
    contain the name of the provider. This name will be placed in http header 'Server'.
  services:
  - rgw
  with_legacy: true
- name: rgw_content_length_compat
  type: bool
  level: advanced
  desc: Multiple content length headers compatibility
  long_desc: Try to handle requests with abiguous multiple content length headers
    (Content-Length, Http-Content-Length).
  fmt_desc: Enable compatibility handling of FCGI requests with both ``CONTENT_LENGTH``
    and ``HTTP_CONTENT_LENGTH`` set.
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_relaxed_region_enforcement
  type: bool
  level: advanced
  desc: Disable region constraint enforcement
  long_desc: Enable requests such as bucket creation to succeed irrespective of region
    restrictions (Jewel compat).
  default: false
  services:
  - rgw
- name: rgw_lifecycle_work_time
  type: str
  level: advanced
  desc: Lifecycle allowed work time
  long_desc: Local time window in which the lifecycle maintenance thread can work.
  default: 00:00-06:00
  services:
  - rgw
  with_legacy: true
- name: rgw_lc_lock_max_time
  type: int
  level: dev
  default: 90
  services:
  - rgw
  with_legacy: true
- name: rgw_lc_thread_delay
  type: int
  level: advanced
  desc: Delay after processing of bucket listing chunks (i.e., per 1000 entries) in
    milliseconds
  default: 0
  services:
  - rgw
- name: rgw_lc_max_worker
  type: int
  level: advanced
  desc: Number of LCWorker tasks that will be run in parallel
  long_desc: Number of LCWorker tasks that will run in parallel--used to permit >1
    bucket/index shards to be processed simultaneously
  fmt_desc: This option specifies the number of lifecycle worker threads
    to run in parallel, thereby processing bucket and index
    shards simultaneously.
  default: 3
  services:
  - rgw
  with_legacy: true
- name: rgw_lc_max_wp_worker
  type: int
  level: advanced
  desc: Number of workpool threads per LCWorker
  long_desc: Number of threads in per-LCWorker workpools--used to accelerate per-bucket
    processing
  fmt_desc: This option specifies the number of threads in each lifecycle
    workers work pool. This option can help accelerate processing each bucket.
  default: 3
  services:
  - rgw
  with_legacy: true
- name: rgw_lc_max_objs
  type: int
  level: advanced
  desc: Number of lifecycle data shards
  long_desc: Number of RADOS objects to use for storing lifecycle index. This affects
    concurrency of lifecycle maintenance, as shards can be processed in parallel.
  default: 32
  services:
  - rgw
  with_legacy: true
- name: rgw_lc_max_rules
  type: uint
  level: advanced
  desc: Max number of lifecycle rules set on one bucket
  long_desc: Number of lifecycle rules set on one bucket should be limited.
  default: 1000
  services:
  - rgw
  with_legacy: true
- name: rgw_lc_debug_interval
  type: int
  level: dev
  desc: The number of seconds that simulate one "day" in order to debug RGW LifeCycle.
    Do *not* modify for a production cluster.
  long_desc: For debugging RGW LifeCycle, the number of seconds that are equivalent to
    one simulated "day". Values less than 1 are ignored and do not change LifeCycle behavior.
    For example, during debugging if one wanted every 10 minutes to be equivalent to one day,
    then this would be set to 600, the number of seconds in 10 minutes.
  default: -1
  services:
  - rgw
  with_legacy: true
- name: rgw_mp_lock_max_time
  type: int
  level: advanced
  desc: Multipart upload max completion time
  long_desc: Time length to allow completion of a multipart upload operation. This
    is done to prevent concurrent completions on the same object with the same upload
    id.
  default: 10_min
  services:
  - rgw
- name: rgw_script_uri
  type: str
  level: dev
  fmt_desc: The alternative value for the ``SCRIPT_URI`` if not set
    in the request.
  services:
  - rgw
  with_legacy: true
- name: rgw_request_uri
  type: str
  level: dev
  fmt_desc: The alternative value for the ``REQUEST_URI`` if not set
    in the request.
  services:
  - rgw
  with_legacy: true
- name: rgw_ignore_get_invalid_range
  type: bool
  level: advanced
  desc: Treat invalid (e.g., negative) range request as full
  long_desc: Treat invalid (e.g., negative) range request as request for the full
    object (AWS compatibility)
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_swift_url
  type: str
  level: advanced
  desc: Swift-auth storage URL
  long_desc: Used in conjunction with rgw internal swift authentication. This affects
    the X-Storage-Url response header value.
  fmt_desc: The URL for the Ceph Object Gateway Swift API.
  services:
  - rgw
  see_also:
  - rgw_swift_auth_entry
  with_legacy: true
- name: rgw_swift_url_prefix
  type: str
  level: advanced
  desc: Swift URL prefix
  long_desc: The URL path prefix for swift requests.
  fmt_desc: |
    The URL prefix for the Swift API, to distinguish it from
    the S3 API endpoint. The default is ``swift``, which
    makes the Swift API available at the URL
    ``http://host:port/swift/v1`` (or
    ``http://host:port/swift/v1/AUTH_%(tenant_id)s`` if
    ``rgw swift account in url`` is enabled).

    For compatibility, setting this configuration variable
    to the empty string causes the default ``swift`` to be
    used; if you do want an empty prefix, set this option to
    ``/``.

    .. warning:: If you set this option to ``/``, you must
                 disable the S3 API by modifying ``rgw
                 enable apis`` to exclude ``s3``. It is not
                 possible to operate radosgw with ``rgw
                 swift url prefix = /`` and simultaneously
                 support both the S3 and Swift APIs. If you
                 do need to support both APIs without
                 prefixes, deploy multiple radosgw instances
                 to listen on different hosts (or ports)
                 instead, enabling some for S3 and some for
                 Swift.
  example: /swift-testing
  default: swift
  services:
  - rgw
  with_legacy: true
- name: rgw_swift_auth_url
  type: str
  level: advanced
  desc: Swift auth URL
  long_desc: Default url to which RGW connects and verifies tokens for v1 auth (if
    not using internal swift auth).
  services:
  - rgw
  with_legacy: true
- name: rgw_swift_auth_entry
  type: str
  level: advanced
  desc: Swift auth URL prefix
  long_desc: URL path prefix for internal swift auth requests.
  fmt_desc: The entry point for a Swift auth URL.
  default: auth
  services:
  - rgw
  see_also:
  - rgw_swift_url
  with_legacy: true
- name: rgw_swift_tenant_name
  type: str
  level: advanced
  desc: Swift tenant name
  long_desc: Tenant name that is used when constructing the swift path.
  services:
  - rgw
  see_also:
  - rgw_swift_account_in_url
  with_legacy: true
- name: rgw_swift_account_in_url
  type: bool
  level: advanced
  desc: Swift account encoded in URL
  long_desc: Whether the swift account is encoded in the uri path (AUTH_<account>).
  fmt_desc: |
    Whether or not the Swift account name should be included
    in the Swift API URL.
    If set to ``false`` (the default), then the Swift API
    will listen on a URL formed like
    ``http://host:port/<rgw_swift_url_prefix>/v1``, and the
    account name (commonly a Keystone project UUID if
    radosgw is configured with `Keystone integration
    <../keystone>`_) will be inferred from request
    headers.
    If set to ``true``, the Swift API URL will be
    ``http://host:port/<rgw_swift_url_prefix>/v1/AUTH_<account_name>``
    (or
    ``http://host:port/<rgw_swift_url_prefix>/v1/AUTH_<keystone_project_id>``)
    instead, and the Keystone ``object-store`` endpoint must
    accordingly be configured to include the
    ``AUTH_%(tenant_id)s`` suffix.
    You **must** set this option to ``true`` (and update the
    Keystone service catalog) if you want radosgw to support
    publicly-readable containers and `temporary URLs
    <../swift/tempurl>`_.
  default: false
  services:
  - rgw
  see_also:
  - rgw_swift_tenant_name
  with_legacy: true
- name: rgw_swift_enforce_content_length
  type: bool
  level: advanced
  desc: Send content length when listing containers (Swift)
  long_desc: Whether content length header is needed when listing containers. When
    this is set to false, RGW will send extra info for each entry in the response.
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_url
  type: str
  level: basic
  desc: The URL to the Keystone server.
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_admin_token
  type: str
  level: advanced
  desc: 'DEPRECATED: The admin token (shared secret) that is used for the Keystone
    requests.'
  fmt_desc: The Keystone admin token (shared secret). In Ceph RGW
    authentication with the admin token has priority over
    authentication with the admin credentials
    (``rgw_keystone_admin_user``, ``rgw_keystone_admin_password``,
    ``rgw_keystone_admin_tenant``, ``rgw_keystone_admin_project``,
    ``rgw_keystone_admin_domain``). The Keystone admin token
    has been deprecated, but can be used to integrate with
    older environments.  It is preferred to instead configure
    ``rgw_keystone_admin_token_path`` to avoid exposing the token.
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_admin_token_path
  type: str
  level: advanced
  desc: Path to a file containing the admin token (shared secret) that is used for
    the Keystone requests.
  fmt_desc: Path to a file containing the Keystone admin token
    (shared secret).  In Ceph RadosGW authentication with
    the admin token has priority over authentication with
    the admin credentials
    (``rgw_keystone_admin_user``, ``rgw_keystone_admin_password``,
    ``rgw_keystone_admin_tenant``, ``rgw_keystone_admin_project``,
    ``rgw_keystone_admin_domain``).
    The Keystone admin token has been deprecated, but can be
    used to integrate with older environments.
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_admin_user
  type: str
  level: advanced
  desc: Keystone admin user.
  fmt_desc: The name of OpenStack user with admin privilege for Keystone
    authentication (Service User) when using OpenStack Identity API v2
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_admin_password
  type: str
  level: advanced
  desc: 'DEPRECATED: Keystone admin password.'
  fmt_desc: The password for OpenStack admin user when using OpenStack
    Identity API v2.  It is preferred to instead configure
    ``rgw_keystone_admin_password_path`` to avoid exposing the token.
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_admin_password_path
  type: str
  level: advanced
  desc: Path to a file containing the Keystone admin password.
  fmt_desc: Path to a file containing the password for OpenStack
    admin user when using OpenStack Identity API v2.
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_admin_tenant
  type: str
  level: advanced
  desc: Keystone admin user tenant.
  fmt_desc: The name of OpenStack tenant with admin privilege (Service Tenant) when
    using OpenStack Identity API v2
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_admin_project
  type: str
  level: advanced
  desc: Keystone admin user project (for Keystone v3).
  fmt_desc: The name of OpenStack project with admin privilege when using
    OpenStack Identity API v3. If left unspecified, value of
    ``rgw keystone admin tenant`` will be used instead.
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_admin_domain
  type: str
  level: advanced
  desc: Keystone admin user domain (for Keystone v3).
  fmt_desc: The name of OpenStack domain with admin privilege when using
    OpenStack Identity API v3.
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_barbican_user
  type: str
  level: advanced
  desc: Keystone user to access barbican secrets.
  fmt_desc: The name of the OpenStack user with access to the `Barbican`_
    secrets used for `Encryption`_.
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_barbican_password
  type: str
  level: advanced
  desc: Keystone password for barbican user.
  fmt_desc: The password associated with the `Barbican`_ user.
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_barbican_tenant
  type: str
  level: advanced
  desc: Keystone barbican user tenant (Keystone v2.0).
  fmt_desc: The name of the OpenStack tenant associated with the `Barbican`_
    user when using OpenStack Identity API v2.
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_barbican_project
  type: str
  level: advanced
  desc: Keystone barbican user project (Keystone v3).
  fmt_desc: The name of the OpenStack project associated with the `Barbican`_
    user when using OpenStack Identity API v3.
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_barbican_domain
  type: str
  level: advanced
  desc: Keystone barbican user domain.
  fmt_desc: The name of the OpenStack domain associated with the `Barbican`_
    user when using OpenStack Identity API v3.
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_api_version
  type: int
  level: advanced
  desc: Version of Keystone API to use (2 or 3).
  fmt_desc: The version (2 or 3) of OpenStack Identity API that should be
    used for communication with the Keystone server.
  default: 2
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_accepted_roles
  type: str
  level: advanced
  desc: Only users with one of these roles will be served when doing Keystone authentication.
  fmt_desc: The roles required to serve requests.
  default: Member, admin
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_accepted_admin_roles
  type: str
  level: advanced
  desc: List of roles allowing user to gain admin privileges (Keystone).
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_token_cache_size
  type: int
  level: advanced
  desc: Keystone token cache size
  long_desc: Max number of Keystone tokens that will be cached. Token that is not
    cached requires RGW to access the Keystone server when authenticating.
  fmt_desc: The maximum number of entries in each Keystone token cache.
  default: 10000
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_verify_ssl
  type: bool
  level: advanced
  desc: Should RGW verify the Keystone server SSL certificate.
  fmt_desc: Verify SSL certificates while making token requests to keystone.
  default: true
  services:
  - rgw
  with_legacy: true
- name: rgw_keystone_implicit_tenants
  type: str
  level: advanced
  desc: RGW Keystone implicit tenants creation
  long_desc: Implicitly create new users in their own tenant with the same name when
    authenticating via Keystone.  Can be limited to s3 or swift only.
  default: 'false'
  services:
  - rgw
  enum_values:
  - 'false'
  - 'true'
  - swift
  - s3
  - both
  - '0'
  - '1'
  - none
  with_legacy: true
- name: rgw_cross_domain_policy
  type: str
  level: advanced
  desc: RGW handle cross domain policy
  long_desc: Returned cross domain policy when accessing the crossdomain.xml resource
    (Swift compatiility).
  default: <allow-access-from domain="*" secure="false" />
  services:
  - rgw
  with_legacy: true
- name: rgw_healthcheck_disabling_path
  type: str
  level: dev
  desc: Swift health check api can be disabled if a file can be accessed in this path.
  services:
  - rgw
  with_legacy: true
- name: rgw_s3_auth_use_rados
  type: bool
  level: advanced
  desc: Should S3 authentication use credentials stored in RADOS backend.
  default: true
  services:
  - rgw
  with_legacy: true
- name: rgw_s3_auth_use_keystone
  type: bool
  level: advanced
  desc: Should S3 authentication use Keystone.
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_s3_auth_order
  type: str
  level: advanced
  desc: Authentication strategy order to use for s3 authentication
  long_desc: Order of authentication strategies to try for s3 authentication, the
    allowed options are a comma separated list of engines external, local. The default
    order is to try all the externally configured engines before attempting local
    rados based authentication
  default: sts, external, local
  services:
  - rgw
  with_legacy: true
- name: rgw_barbican_url
  type: str
  level: advanced
  desc: URL to barbican server.
  fmt_desc: The URL for the Barbican server.
  services:
  - rgw
  with_legacy: true
# OpenLDAP-style LDAP parameter strings
- name: rgw_ldap_uri
  type: str
  level: advanced
  desc: Space-separated list of LDAP servers in URI format.
  default: ldaps://<ldap.your.domain>
  services:
  - rgw
  with_legacy: true
- name: rgw_ldap_binddn
  type: str
  level: advanced
  desc: LDAP entry RGW will bind with (user match).
  default: uid=admin,cn=users,dc=example,dc=com
  services:
  - rgw
  with_legacy: true
- name: rgw_ldap_searchdn
  type: str
  level: advanced
  desc: LDAP search base (basedn).
  default: cn=users,cn=accounts,dc=example,dc=com
  services:
  - rgw
  with_legacy: true
- name: rgw_ldap_dnattr
  type: str
  level: advanced
  desc: LDAP attribute containing RGW user names (to form binddns).
  default: uid
  services:
  - rgw
  with_legacy: true
- name: rgw_ldap_secret
  type: str
  level: advanced
  desc: Path to file containing credentials for rgw_ldap_binddn.
  default: /etc/openldap/secret
  services:
  - rgw
  with_legacy: true
- name: rgw_s3_auth_use_ldap
  type: bool
  level: advanced
  desc: Should S3 authentication use LDAP.
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_ldap_searchfilter
  type: str
  level: advanced
  desc: LDAP search filter.
  services:
  - rgw
  with_legacy: true
- name: rgw_opa_url
  type: str
  level: advanced
  desc: URL to OPA server.
  services:
  - rgw
  with_legacy: true
- name: rgw_opa_token
  type: str
  level: advanced
  desc: The Bearer token OPA uses to authenticate client requests.
  services:
  - rgw
  with_legacy: true
- name: rgw_opa_verify_ssl
  type: bool
  level: advanced
  desc: Should RGW verify the OPA server SSL certificate.
  default: true
  services:
  - rgw
  with_legacy: true
- name: rgw_use_opa_authz
  type: bool
  level: advanced
  desc: Should OPA be used to authorize client requests.
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_admin_entry
  type: str
  level: advanced
  desc: Path prefix to be used for accessing RGW RESTful admin API.
  fmt_desc: The entry point for an admin request URL.
  default: admin
  services:
  - rgw
  with_legacy: true
- name: rgw_enforce_swift_acls
  type: bool
  level: advanced
  desc: RGW enforce swift acls
  long_desc: Should RGW enforce special Swift-only ACLs. Swift has a special ACL that
    gives permission to access all objects in a container.
  fmt_desc: Enforces the Swift Access Control List (ACL) settings.
  default: true
  services:
  - rgw
  with_legacy: true
- name: rgw_swift_token_expiration
  type: int
  level: advanced
  desc: Expiration time (in seconds) for token generated through RGW Swift auth.
  fmt_desc: The time in seconds for expiring a Swift token.
  default: 1_day
  services:
  - rgw
  with_legacy: true
- name: rgw_print_continue
  type: bool
  level: advanced
  desc: RGW support of 100-continue
  long_desc: Should RGW explicitly send 100 (continue) responses. This is mainly relevant
    when using FastCGI, as some FastCGI modules do not fully support this feature.
  fmt_desc: Enable ``100-continue`` if it is operational.
  default: true
  services:
  - rgw
  with_legacy: true
- name: rgw_print_prohibited_content_length
  type: bool
  level: advanced
  desc: RGW RFC-7230 compatibility
  long_desc: Specifies whether RGW violates RFC 7230 and sends Content-Length with
    204 or 304 statuses.
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_remote_addr_param
  type: str
  level: advanced
  desc: HTTP header that holds the remote address in incoming requests.
  long_desc: RGW will use this header to extract requests origin. When RGW runs behind
    a reverse proxy, the remote address header will point at the proxy's address and
    not at the originator's address. Therefore it is sometimes possible to have the
    proxy add the originator's address in a separate HTTP header, which will allow
    RGW to log it correctly.
  fmt_desc: The remote address parameter. For example, the HTTP field
    containing the remote address, or the ``X-Forwarded-For``
    address if a reverse proxy is operational.
  default: REMOTE_ADDR
  services:
  - rgw
  see_also:
  - rgw_enable_ops_log
  with_legacy: true
- name: rgw_op_thread_timeout
  type: int
  level: dev
  desc: Timeout for async rados coroutine operations.
  fmt_desc: The timeout in seconds for open threads.
  default: 10_min
  services:
  - rgw
  with_legacy: true
- name: rgw_op_thread_suicide_timeout
  type: int
  level: dev
  default: 0
  fmt_desc: The time ``timeout`` in seconds before a Ceph Object Gateway
    process dies. Disabled if set to ``0``.
  services:
  - rgw
  with_legacy: true
- name: rgw_thread_pool_size
  type: int
  level: basic
  desc: RGW requests handling thread pool size.
  long_desc: This parameter determines the number of concurrent requests RGW can process
    when using either the civetweb, or the fastcgi frontends. The higher this number
    is, RGW will be able to deal with more concurrent requests at the cost of more
    resource utilization.
  fmt_desc: The size of the thread pool.
  default: 512
  services:
  - rgw
  with_legacy: true
- name: rgw_num_control_oids
  type: int
  level: advanced
  desc: Number of control objects used for cross-RGW communication.
  long_desc: RGW uses certain control objects to send messages between different RGW
    processes running on the same zone. These messages include metadata cache invalidation
    info that is being sent when metadata is modified (such as user or bucket information).
    A higher number of control objects allows better concurrency of these messages,
    at the cost of more resource utilization.
  fmt_desc: The number of notification objects used for cache synchronization
    between different ``rgw`` instances.
  default: 8
  services:
  - rgw
  with_legacy: true
- name: rgw_verify_ssl
  type: bool
  level: advanced
  desc: Should RGW verify SSL when connecing to a remote HTTP server
  long_desc: RGW can send requests to other RGW servers (e.g., in multi-site sync
    work). This configurable selects whether RGW should verify the certificate for
    the remote peer and host.
  fmt_desc: Verify SSL certificates while making requests.
  default: true
  services:
  - rgw
  see_also:
  - rgw_keystone_verify_ssl
  with_legacy: true
# The following are tunables for caches of RGW NFS (and other file
# client) objects.
#
# The file handle cache is a partitioned hash table
# (fhcache_partitions), each with a closed hash part and backing
# b-tree mapping.  The number of partions is expected to be a small
# prime, the cache size something larger but less than 5K, the total
# size of the cache is n_part *  cache_size.
- name: rgw_nfs_lru_lanes
  type: int
  level: advanced
  default: 5
  services:
  - rgw
  with_legacy: true
- name: rgw_nfs_lru_lane_hiwat
  type: int
  level: advanced
  default: 911
  services:
  - rgw
  with_legacy: true
- name: rgw_nfs_fhcache_partitions
  type: int
  level: advanced
  default: 3
  services:
  - rgw
  with_legacy: true
- name: rgw_nfs_fhcache_size
  type: int
  level: advanced
  default: 2017
  services:
  - rgw
  with_legacy: true
- name: rgw_nfs_namespace_expire_secs
  type: int
  level: advanced
  default: 5_min
  services:
  - rgw
  min: 1
  with_legacy: true
- name: rgw_nfs_max_gc
  type: int
  level: advanced
  default: 5_min
  services:
  - rgw
  min: 1
  with_legacy: true
- name: rgw_nfs_write_completion_interval_s
  type: int
  level: advanced
  default: 10
  services:
  - rgw
  with_legacy: true
# use fast S3 attrs from bucket index--currently assumes NFS mounts are immutable
- name: rgw_nfs_s3_fast_attrs
  type: bool
  level: advanced
  desc: use fast S3 attrs from bucket index (immutable only)
  long_desc: use fast S3 attrs from bucket index (assumes NFS mounts are immutable)
  default: false
  services:
  - rgw
  with_legacy: true
# overrides for librgw/nfs
- name: rgw_nfs_run_gc_threads
  type: bool
  level: advanced
  desc: run GC threads in librgw (default off)
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_nfs_run_lc_threads
  type: bool
  level: advanced
  desc: run lifecycle threads in librgw (default off)
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_nfs_run_quota_threads
  type: bool
  level: advanced
  desc: run quota threads in librgw (default off)
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_nfs_run_sync_thread
  type: bool
  level: advanced
  desc: run sync thread in librgw (default off)
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_rados_pool_autoscale_bias
  type: float
  level: advanced
  desc: pg_autoscale_bias value for RGW metadata (omap-heavy) pools
  default: 4
  services:
  - rgw
  min: 0.01
  max: 100000
- name: rgw_rados_pool_recovery_priority
  type: uint
  level: advanced
  desc: recovery_priority value for RGW metadata (omap-heavy) pools
  default: 5
  services:
  - rgw
  min: -10
  max: 10
- name: rgw_zone
  type: str
  level: advanced
  desc: Zone name
  fmt_desc: The name of the zone for the gateway instance. If no zone is
    set, a cluster-wide default can be configured with the command
    ``radosgw-admin zone default``.
  services:
  - rgw
  see_also:
  - rgw_zonegroup
  - rgw_realm
  with_legacy: true
- name: rgw_zone_id
  type: str
  level: advanced
  desc: Zone ID
  services:
  - rgw
  see_also:
  - rgw_zone
  - rgw_zonegroup
  - rgw_realm
- name: rgw_zone_root_pool
  type: str
  level: advanced
  desc: Zone root pool name
  long_desc: The zone root pool, is the pool where the RGW zone configuration located.
  default: .rgw.root
  services:
  - rgw
  see_also:
  - rgw_zonegroup_root_pool
  - rgw_realm_root_pool
  - rgw_period_root_pool
  with_legacy: true
- name: rgw_default_zone_info_oid
  type: str
  level: advanced
  desc: Default zone info object id
  long_desc: Name of the RADOS object that holds the default zone information.
  default: default.zone
  services:
  - rgw
  with_legacy: true
- name: rgw_region
  type: str
  level: advanced
  desc: Region name
  long_desc: Obsolete config option. The rgw_zonegroup option should be used instead.
  services:
  - rgw
  see_also:
  - rgw_zonegroup
  with_legacy: true
- name: rgw_region_root_pool
  type: str
  level: advanced
  desc: Region root pool
  long_desc: Obsolete config option. The rgw_zonegroup_root_pool should be used instead.
  default: .rgw.root
  services:
  - rgw
  see_also:
  - rgw_zonegroup_root_pool
  with_legacy: true
- name: rgw_default_region_info_oid
  type: str
  level: advanced
  desc: Default region info object id
  long_desc: Obsolete config option. The rgw_default_zonegroup_info_oid should be
    used instead.
  default: default.region
  services:
  - rgw
  see_also:
  - rgw_default_zonegroup_info_oid
  with_legacy: true
- name: rgw_zonegroup
  type: str
  level: advanced
  desc: Zonegroup name
  fmt_desc: The name of the zonegroup for the gateway instance. If no
    zonegroup is set, a cluster-wide default can be configured with
    the command ``radosgw-admin zonegroup default``.
  services:
  - rgw
  see_also:
  - rgw_zone
  - rgw_realm
  with_legacy: true
- name: rgw_zonegroup_id
  type: str
  level: advanced
  desc: Zonegroup ID
  services:
  - rgw
  see_also:
  - rgw_zone
  - rgw_zonegroup
  - rgw_realm
- name: rgw_zonegroup_root_pool
  type: str
  level: advanced
  desc: Zonegroup root pool
  long_desc: The zonegroup root pool, is the pool where the RGW zonegroup configuration
    located.
  default: .rgw.root
  services:
  - rgw
  see_also:
  - rgw_zone_root_pool
  - rgw_realm_root_pool
  - rgw_period_root_pool
  with_legacy: true
- name: rgw_default_zonegroup_info_oid
  type: str
  level: advanced
  default: default.zonegroup
  services:
  - rgw
  with_legacy: true
- name: rgw_realm
  type: str
  level: advanced
  fmt_desc: The name of the realm for the gateway instance. If no realm is
    set, a cluster-wide default can be configured with the command
    ``radosgw-admin realm default``.
  services:
  - rgw
  with_legacy: true
- name: rgw_realm_id
  type: str
  level: advanced
  services:
  - rgw
- name: rgw_realm_root_pool
  type: str
  level: advanced
  desc: Realm root pool
  long_desc: The realm root pool, is the pool where the RGW realm configuration located.
  default: .rgw.root
  services:
  - rgw
  see_also:
  - rgw_zonegroup_root_pool
  - rgw_zone_root_pool
  - rgw_period_root_pool
  with_legacy: true
- name: rgw_default_realm_info_oid
  type: str
  level: advanced
  default: default.realm
  services:
  - rgw
  with_legacy: true
- name: rgw_period_root_pool
  type: str
  level: advanced
  desc: Period root pool
  long_desc: The period root pool, is the pool where the RGW period configuration
    located.
  default: .rgw.root
  services:
  - rgw
  see_also:
  - rgw_zonegroup_root_pool
  - rgw_zone_root_pool
  - rgw_realm_root_pool
  with_legacy: true
- name: rgw_period_latest_epoch_info_oid
  type: str
  level: dev
  default: .latest_epoch
  services:
  - rgw
  with_legacy: true
- name: rgw_log_nonexistent_bucket
  type: bool
  level: advanced
  desc: Should RGW log operations on bucket that does not exist
  long_desc: This config option applies to the ops log. When this option is set, the
    ops log will log operations that are sent to non existing buckets. These operations
    inherently fail, and do not correspond to a specific user.
  fmt_desc: Enables Ceph Object Gateway to log a request for a non-existent
    bucket.
  default: false
  services:
  - rgw
  see_also:
  - rgw_enable_ops_log
  with_legacy: true
# man date to see codes (a subset are supported)
- name: rgw_log_object_name
  type: str
  level: advanced
  desc: Ops log object name format
  long_desc: Defines the format of the RADOS objects names that ops log uses to store
    ops log data
  fmt_desc: The logging format for an object name. See ma npage
    :manpage:`date` for details about format specifiers.
  default: '%Y-%m-%d-%H-%i-%n'
  services:
  - rgw
  see_also:
  - rgw_enable_ops_log
  with_legacy: true
- name: rgw_log_object_name_utc
  type: bool
  level: advanced
  desc: Should ops log object name based on UTC
  long_desc: If set, the names of the RADOS objects that hold the ops log data will
    be based on UTC time zone. If not set, it will use the local time zone.
  fmt_desc: Whether a logged object name includes a UTC time.
    If ``false``, it uses the local time.
  default: false
  services:
  - rgw
  see_also:
  - rgw_enable_ops_log
  - rgw_log_object_name
  with_legacy: true
- name: rgw_usage_max_shards
  type: int
  level: advanced
  desc: Number of shards for usage log.
  long_desc: The number of RADOS objects that RGW will use in order to store the usage
    log data.
  fmt_desc: The maximum number of shards for usage logging.
  default: 32
  services:
  - rgw
  see_also:
  - rgw_enable_usage_log
  with_legacy: true
- name: rgw_usage_max_user_shards
  type: int
  level: advanced
  desc: Number of shards for single user in usage log
  long_desc: The number of shards that a single user will span over in the usage log.
  fmt_desc: The maximum number of shards used for a single user's
    usage logging.
  default: 1
  services:
  - rgw
  see_also:
  - rgw_enable_usage_log
  min: 1
  with_legacy: true
# enable logging every rgw operation
- name: rgw_enable_ops_log
  type: bool
  level: advanced
  desc: Enable ops log
  fmt_desc: Enable logging for each successful Ceph Object Gateway operation.
  default: false
  services:
  - rgw
  see_also:
  - rgw_log_nonexistent_bucket
  - rgw_log_object_name
  - rgw_ops_log_rados
  - rgw_ops_log_socket_path
  - rgw_ops_log_file_path
  with_legacy: true
# enable logging bandwidth usage
- name: rgw_enable_usage_log
  type: bool
  level: advanced
  desc: Enable the usage log
  default: false
  services:
  - rgw
  see_also:
  - rgw_usage_max_shards
  with_legacy: true
# whether ops log should go to rados
- name: rgw_ops_log_rados
  type: bool
  level: advanced
  desc: Use RADOS for ops log
  long_desc: If set, RGW will store ops log information in RADOS.
  fmt_desc: Whether the operations log should be written to the
    Ceph Storage Cluster backend.
  default: true
  services:
  - rgw
  see_also:
  - rgw_enable_ops_log
  with_legacy: true
# path to unix domain socket where ops log can go
- name: rgw_ops_log_socket_path
  type: str
  level: advanced
  desc: Unix domain socket path for ops log.
  long_desc: Path to unix domain socket that RGW will listen for connection on. When
    connected, RGW will send ops log data through it.
  fmt_desc: The Unix domain socket for writing operations logs.
  services:
  - rgw
  see_also:
  - rgw_enable_ops_log
  - rgw_ops_log_data_backlog
  with_legacy: true
# path to file where ops log can go
- name: rgw_ops_log_file_path
  type: str
  level: advanced
  desc: File-system path for ops log.
  long_desc: Path to file that RGW will log ops logs to.
  fmt_desc: The file-system path for writing operations logs.
  services:
  - rgw
  see_also:
  - rgw_enable_ops_log
  with_legacy: true
# max data backlog for ops log
- name: rgw_ops_log_data_backlog
  type: size
  level: advanced
  desc: Ops log socket backlog
  long_desc: Maximum amount of data backlog that RGW can keep when ops log is configured
    to send info through unix domain socket. When data backlog is higher than this,
    ops log entries will be lost. In order to avoid ops log information loss, the
    listener needs to clear data (by reading it) quickly enough.
  fmt_desc: The maximum data backlog data size for operations logs written
    to a Unix domain socket.
  default: 5_M
  services:
  - rgw
  see_also:
  - rgw_enable_ops_log
  - rgw_ops_log_socket_path
  with_legacy: true
- name: rgw_usage_log_flush_threshold
  type: int
  level: advanced
  desc: Number of entries in usage log before flushing
  long_desc: This is the max number of entries that will be held in the usage log,
    before it will be flushed to the backend. Note that the usage log is periodically
    flushed, even if number of entries does not reach this threshold. A usage log
    entry corresponds to one or more operations on a single bucket.i
  fmt_desc: The number of dirty merged entries in the usage log before
    flushing synchronously.
  default: 1024
  services:
  - rgw
  see_also:
  - rgw_enable_usage_log
  - rgw_usage_log_tick_interval
  with_legacy: true
- name: rgw_usage_log_tick_interval
  type: int
  level: advanced
  desc: Number of seconds between usage log flush cycles
  long_desc: The number of seconds between consecutive usage log flushes. The usage
    log will also flush itself to the backend if the number of pending entries reaches
    a certain threshold.
  fmt_desc: Flush pending usage log data every ``n`` seconds.
  default: 30
  services:
  - rgw
  see_also:
  - rgw_enable_usage_log
  - rgw_usage_log_flush_threshold
  with_legacy: true
- name: rgw_init_timeout
  type: int
  level: basic
  desc: Initialization timeout
  long_desc: The time length (in seconds) that RGW will allow for its initialization.
    RGW process will give up and quit if initialization is not complete after this
    amount of time.
  fmt_desc: The number of seconds before Ceph Object Gateway gives up on
    initialization.
  default: 5_min
  services:
  - rgw
  with_legacy: true
- name: rgw_mime_types_file
  type: str
  level: basic
  desc: Path to local mime types file
  long_desc: The mime types file is needed in Swift when uploading an object. If object's
    content type is not specified, RGW will use data from this file to assign a content
    type to the object.
  fmt_desc: The path and location of the MIME-types file. Used for Swift
    auto-detection of object types.
  default: /etc/mime.types
  services:
  - rgw
  with_legacy: true
- name: rgw_gc_max_objs
  type: int
  level: advanced
  desc: Number of shards for garbage collector data
  long_desc: The number of garbage collector data shards, is the number of RADOS objects
    that RGW will use to store the garbage collection information on.
  fmt_desc: The maximum number of objects that may be handled by
    garbage collection in one garbage collection processing cycle.
    Please do not change this value after the first deployment.
  default: 32
  services:
  - rgw
  see_also:
  - rgw_gc_obj_min_wait
  - rgw_gc_processor_max_time
  - rgw_gc_processor_period
  - rgw_gc_max_concurrent_io
  with_legacy: true
# wait time before object may be handled by gc, recommended lower limit is 30 mins
- name: rgw_gc_obj_min_wait
  type: int
  level: advanced
  desc: Garbage collection object expiration time
  long_desc: The length of time (in seconds) that the RGW collector will wait before
    purging a deleted object's data. RGW will not remove object immediately, as object
    could still have readers. A mechanism exists to increase the object's expiration
    time when it's being read. The recommended value of its lower limit is 30 minutes
  fmt_desc: The minimum wait time before a deleted object may be removed
    and handled by garbage collection processing.
  default: 2_hr
  services:
  - rgw
  see_also:
  - rgw_gc_max_objs
  - rgw_gc_processor_max_time
  - rgw_gc_processor_period
  - rgw_gc_max_concurrent_io
  with_legacy: true
- name: rgw_gc_processor_max_time
  type: int
  level: advanced
  desc: Length of time GC processor can lease shard
  long_desc: Garbage collection thread in RGW process holds a lease on its data shards.
    These objects contain the information about the objects that need to be removed.
    RGW takes a lease in order to prevent multiple RGW processes from handling the
    same objects concurrently. This time signifies that maximum amount of time (in
    seconds) that RGW is allowed to hold that lease. In the case where RGW goes down
    uncleanly, this is the amount of time where processing of that data shard will
    be blocked.
  fmt_desc: The maximum time between the beginning of two consecutive garbage
    collection processing cycles.
  default: 1_hr
  services:
  - rgw
  see_also:
  - rgw_gc_max_objs
  - rgw_gc_obj_min_wait
  - rgw_gc_processor_period
  - rgw_gc_max_concurrent_io
  with_legacy: true
- name: rgw_gc_processor_period
  type: int
  level: advanced
  desc: Garbage collector cycle run time
  long_desc: The amount of time between the start of consecutive runs of the garbage
    collector threads. If garbage collector runs takes more than this period, it will
    not wait before running again.
  fmt_desc: The cycle time for garbage collection processing.
  default: 1_hr
  services:
  - rgw
  see_also:
  - rgw_gc_max_objs
  - rgw_gc_obj_min_wait
  - rgw_gc_processor_max_time
  - rgw_gc_max_concurrent_io
  - rgw_gc_max_trim_chunk
  with_legacy: true
- name: rgw_gc_max_concurrent_io
  type: int
  level: advanced
  desc: Max concurrent RADOS IO operations for garbage collection
  long_desc: The maximum number of concurrent IO operations that the RGW garbage collection
    thread will use when purging old data.
  default: 10
  services:
  - rgw
  see_also:
  - rgw_gc_max_objs
  - rgw_gc_obj_min_wait
  - rgw_gc_processor_max_time
  - rgw_gc_max_trim_chunk
  with_legacy: true
- name: rgw_gc_max_trim_chunk
  type: int
  level: advanced
  desc: Max number of keys to remove from garbage collector log in a single operation
  default: 16
  services:
  - rgw
  see_also:
  - rgw_gc_max_objs
  - rgw_gc_obj_min_wait
  - rgw_gc_processor_max_time
  - rgw_gc_max_concurrent_io
  with_legacy: true
- name: rgw_gc_max_deferred_entries_size
  type: uint
  level: advanced
  desc: maximum allowed size of deferred entries in queue head for gc
  default: 3_K
  services:
  - rgw
  with_legacy: true
- name: rgw_gc_max_queue_size
  type: uint
  level: advanced
  desc: Maximum allowed queue size for gc
  long_desc: The maximum allowed size of each gc queue, and its value should not be
    greater than (osd_max_object_size - rgw_gc_max_deferred_entries_size - 1K).
  default: 131068_K
  services:
  - rgw
  see_also:
  - osd_max_object_size
  - rgw_gc_max_deferred_entries_size
  with_legacy: true
- name: rgw_gc_max_deferred
  type: uint
  level: advanced
  desc: Number of maximum deferred data entries to be stored in queue for gc
  default: 50
  services:
  - rgw
  with_legacy: true
- name: rgw_s3_success_create_obj_status
  type: int
  level: advanced
  desc: HTTP return code override for object creation
  long_desc: If not zero, this is the HTTP return code that will be returned on a
    successful S3 object creation.
  fmt_desc: The alternate success status response for ``create-obj``.
  default: 0
  services:
  - rgw
  with_legacy: true
- name: rgw_s3_client_max_sig_ver
  type: int
  level: advanced
  desc: Max S3 authentication signature version
  long_desc: If greater than zero, would force max signature version to use
  default: -1
  services:
  - rgw
- name: rgw_resolve_cname
  type: bool
  level: advanced
  desc: Support vanity domain names via CNAME
  long_desc: If true, RGW will query DNS when detecting that it's serving a request
    that was sent to a host in another domain. If a CNAME record is configured for
    that domain it will use it instead. This gives user to have the ability of creating
    a unique domain of their own to point at data in their bucket.
  fmt_desc: Whether ``rgw`` should use DNS CNAME record of the request
    hostname field (if hostname is not equal to ``rgw dns name``).
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_obj_stripe_size
  type: size
  level: advanced
  desc: RGW object stripe size
  long_desc: The size of an object stripe for RGW objects. This is the maximum size
    a backing RADOS object will have. RGW objects that are larger than this will span
    over multiple objects.
  fmt_desc: The size of an object stripe for Ceph Object Gateway objects.
    See `Architecture`_ for details on striping.
  default: 4_M
  services:
  - rgw
  with_legacy: true
# list of extended attrs that can be set on objects (beyond the default)
- name: rgw_extended_http_attrs
  type: str
  level: advanced
  desc: RGW support extended HTTP attrs
  long_desc: Add new set of attributes that could be set on an object. These extra
    attributes can be set through HTTP header fields when putting the objects. If
    set, these attributes will return as HTTP fields when doing GET/HEAD on the object.
  fmt_desc: Add new set of attributes that could be set on an entity
    (user, bucket or object). These extra attributes can be set
    through HTTP header fields when putting the entity or modifying
    it using POST method. If set, these attributes will return as
    HTTP  fields when doing GET/HEAD on the entity.
  services:
  - rgw
  example: content_foo, content_bar, x-foo-bar
  with_legacy: true
- name: rgw_exit_timeout_secs
  type: int
  level: advanced
  desc: RGW shutdown timeout
  long_desc: Number of seconds to wait for a process before exiting unconditionally.
  default: 2_min
  services:
  - rgw
  with_legacy: true
- name: rgw_get_obj_window_size
  type: size
  level: advanced
  desc: RGW object read window size
  long_desc: The window size in bytes for a single object read request
  default: 16_M
  services:
  - rgw
  with_legacy: true
- name: rgw_get_obj_max_req_size
  type: size
  level: advanced
  desc: RGW object read chunk size
  long_desc: The maximum request size of a single object read operation sent to RADOS
  fmt_desc: The maximum request size of a single get operation sent to the
    Ceph Storage Cluster.
  default: 4_M
  services:
  - rgw
  with_legacy: true
- name: rgw_relaxed_s3_bucket_names
  type: bool
  level: advanced
  desc: RGW enable relaxed S3 bucket names
  long_desc: RGW enable relaxed S3 bucket name rules for US region buckets.
  fmt_desc: Enables relaxed S3 bucket names rules for US region buckets.
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_defer_to_bucket_acls
  type: str
  level: advanced
  desc: Bucket ACLs override object ACLs
  long_desc: If not empty, a string that selects that mode of operation. 'recurse'
    will use bucket's ACL for the authorizaton. 'full-control' will allow users that
    users that have full control permission on the bucket have access to the object.
  services:
  - rgw
  with_legacy: true
- name: rgw_list_buckets_max_chunk
  type: int
  level: advanced
  desc: Max number of buckets to retrieve in a single listing operation
  long_desc: When RGW fetches lists of user's buckets from the backend, this is the
    max number of entries it will try to retrieve in a single operation. Note that
    the backend may choose to return a smaller number of entries.
  fmt_desc: The maximum number of buckets to retrieve in a single operation
    when listing user buckets.
  default: 1000
  services:
  - rgw
  with_legacy: true
- name: rgw_md_log_max_shards
  type: int
  level: advanced
  desc: RGW number of metadata log shards
  long_desc: The number of shards the RGW metadata log entries will reside in. This
    affects the metadata sync parallelism as a shard can only be processed by a single
    RGW at a time
  fmt_desc: The maximum number of shards for the metadata log.
  default: 64
  services:
  - rgw
  with_legacy: true
- name: rgw_curl_buffersize
  type: int
  level: dev
  long_desc: 'Pass a long specifying your preferred size (in bytes) for the receivebuffer
    in libcurl. See: https://curl.se/libcurl/c/CURLOPT_BUFFERSIZE.html'
  default: 524288
  services:
  - rgw
  min: 1024
  max: 524288
  with_legacy: true
- name: rgw_curl_wait_timeout_ms
  type: int
  level: dev
  default: 1000
  fmt_desc: The timeout in milliseconds for certain ``curl`` calls.
  services:
  - rgw
  with_legacy: true
- name: rgw_curl_low_speed_limit
  type: int
  level: advanced
  long_desc: It contains the average transfer speed in bytes per second that the transfer
    should be below during rgw_curl_low_speed_time seconds for libcurl to consider
    it to be too slow and abort. Set it zero to disable this.
  default: 1024
  services:
  - rgw
  with_legacy: true
- name: rgw_curl_low_speed_time
  type: int
  level: advanced
  long_desc: It contains the time in number seconds that the transfer speed should
    be below the rgw_curl_low_speed_limit for the library to consider it too slow
    and abort. Set it zero to disable this.
  default: 5_min
  services:
  - rgw
  with_legacy: true
- name: rgw_copy_obj_progress
  type: bool
  level: advanced
  desc: Send progress report through copy operation
  long_desc: If true, RGW will send progress information when copy operation is executed.
  fmt_desc: Enables output of object progress during long copy operations.
  default: true
  services:
  - rgw
  with_legacy: true
- name: rgw_copy_obj_progress_every_bytes
  type: size
  level: advanced
  desc: Send copy-object progress info after these many bytes
  fmt_desc: The minimum bytes between copy progress output.
  default: 1_M
  services:
  - rgw
  with_legacy: true
- name: rgw_sync_obj_etag_verify
  type: bool
  level: advanced
  desc: Verify if the object copied from remote is identical to its source
  long_desc: If true, this option computes the MD5 checksum of the data which is written
    at the destination and checks if it is identical to the ETAG stored in the source.
    It ensures integrity of the objects fetched from a remote server over HTTP including
    multisite sync.
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_obj_tombstone_cache_size
  type: int
  level: advanced
  desc: Max number of entries to keep in tombstone cache
  long_desc: The tombstone cache is used when doing a multi-zone data sync. RGW keeps
    there information about removed objects which is needed in order to prevent re-syncing
    of objects that were already removed.
  default: 1000
  services:
  - rgw
  with_legacy: true
- name: rgw_data_log_window
  type: int
  level: advanced
  desc: Data log time window
  long_desc: The data log keeps information about buckets that have objectst that
    were modified within a specific timeframe. The sync process then knows which buckets
    are needed to be scanned for data sync.
  fmt_desc: The data log entries window in seconds.
  default: 30
  services:
  - rgw
  with_legacy: true
- name: rgw_data_log_changes_size
  type: int
  level: dev
  desc: Max size of pending changes in data log
  long_desc: RGW will trigger update to the data log if the number of pending entries
    reached this number.
  fmt_dsec: The number of in-memory entries to hold for the data changes log.
  default: 1000
  services:
  - rgw
  with_legacy: true
- name: rgw_data_log_num_shards
  type: int
  level: advanced
  desc: Number of data log shards
  long_desc: The number of shards the RGW data log entries will reside in. This affects
    the data sync parallelism as a shard can only be processed by a single RGW at
    a time.
  fmt_desc: The number of shards (objects) on which to keep the
    data changes log.
  default: 128
  services:
  - rgw
  with_legacy: true
- name: rgw_data_log_obj_prefix
  type: str
  level: dev
  default: data_log
  fmt_desc: The object name prefix for the data log.
  services:
  - rgw
  with_legacy: true
- name: rgw_bucket_quota_ttl
  type: int
  level: advanced
  desc: Bucket quota stats cache TTL
  long_desc: Length of time for bucket stats to be cached within RGW instance.
  fmt_desc: The amount of time in seconds cached quota information is
    trusted.  After this timeout, the quota information will be
    re-fetched from the cluster.
  default: 10_min
  services:
  - rgw
  with_legacy: true
- name: rgw_bucket_quota_cache_size
  type: int
  level: advanced
  desc: RGW quota stats cache size
  long_desc: Maximum number of entries in the quota stats cache.
  default: 10000
  services:
  - rgw
  with_legacy: true
- name: rgw_bucket_default_quota_max_objects
  type: int
  level: basic
  desc: Default quota for max objects in a bucket
  long_desc: The default quota configuration for max number of objects in a bucket.
    A negative number means 'unlimited'.
  fmt_desc: Default max number of objects per bucket. Set on new users,
    if no other quota is specified. Has no effect on existing users.
    This variable should be set in the client or global sections
    so that it is automatically applied to radosgw-admin commands.
  default: -1
  services:
  - rgw
  with_legacy: true
- name: rgw_bucket_default_quota_max_size
  type: int
  level: advanced
  desc: Default quota for total size in a bucket
  long_desc: The default quota configuration for total size of objects in a bucket.
    A negative number means 'unlimited'.
  fmt_desc: Default max capacity per bucket, in bytes. Set on new users,
    if no other quota is specified. Has no effect on existing users.
  default: -1
  services:
  - rgw
  with_legacy: true
- name: rgw_expose_bucket
  type: bool
  level: advanced
  desc: Send Bucket HTTP header with the response
  long_desc: If true, RGW will send a Bucket HTTP header with the responses. The header
    will contain the name of the bucket the operation happened on.
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_frontends
  type: str
  level: basic
  desc: RGW frontends configuration
  long_desc: A comma delimited list of frontends configuration. Each configuration
    contains the type of the frontend followed by an optional space delimited set
    of key=value config parameters.
  fmt_desc: Configures the HTTP frontend(s). The configuration for multiple
    frontends can be provided in a comma-delimited list. Each frontend
    configuration may include a list of options separated by spaces,
    where each option is in the form "key=value" or "key". See
    `HTTP Frontends`_ for more on supported options.
  default: beast port=7480
  services:
  - rgw
  with_legacy: true
- name: rgw_frontend_defaults
  type: str
  level: advanced
  desc: RGW frontends default configuration
  long_desc: A comma delimited list of default frontends configuration.
  default: beast ssl_certificate=config://rgw/cert/$realm/$zone.crt ssl_private_key=config://rgw/cert/$realm/$zone.key
  services:
  - rgw
- name: rgw_beast_enable_async
  type: bool
  level: dev
  desc: Enable async request processing under beast using coroutines
  long_desc: When enabled, the beast frontend will process requests using
    coroutines, allowing the concurrent processing of several requests on the
    same thread. When disabled, the number of concurrent requests will be
    limited by the thread count, but debugging and tracing the synchronous
    calls can be easier.
  default: true
  services:
  - rgw
  with_legacy: true
- name: rgw_user_quota_bucket_sync_interval
  type: int
  level: advanced
  desc: User quota bucket sync interval
  long_desc: Time period for accumulating modified buckets before syncing these stats.
  fmt_desc: The amount of time in seconds bucket quota information is
    accumulated before syncing to the cluster.  During this time,
    other RGW instances will not see the changes in bucket quota
    stats from operations on this instance.
  default: 3_min
  services:
  - rgw
  with_legacy: true
- name: rgw_user_quota_sync_interval
  type: int
  level: advanced
  desc: User quota sync interval
  long_desc: Time period for accumulating modified buckets before syncing entire user
    stats.
  fmt_desc: The amount of time in seconds user quota information is
    accumulated before syncing to the cluster.  During this time,
    other RGW instances will not see the changes in user quota stats
    from operations on this instance.
  default: 1_day
  services:
  - rgw
  with_legacy: true
- name: rgw_user_quota_sync_idle_users
  type: bool
  level: advanced
  desc: Should sync idle users quota
  long_desc: Whether stats for idle users be fully synced.
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_user_quota_sync_wait_time
  type: int
  level: advanced
  desc: User quota full-sync wait time
  long_desc: Minimum time between two full stats sync for non-idle users.
  default: 1_day
  services:
  - rgw
  with_legacy: true
- name: rgw_user_default_quota_max_objects
  type: int
  level: basic
  desc: User quota max objects
  long_desc: The default quota configuration for total number of objects for a single
    user. A negative number means 'unlimited'.
  fmt_desc: Default max number of objects for a user. This includes all
    objects in all buckets owned by the user. Set on new users,
    if no other quota is specified. Has no effect on existing users.
  default: -1
  services:
  - rgw
  with_legacy: true
- name: rgw_user_default_quota_max_size
  type: int
  level: basic
  desc: User quota max size
  long_desc: The default quota configuration for total size of objects for a single
    user. A negative number means 'unlimited'.
  fmt_desc: The value for user max size quota in bytes set on new users,
    if no other quota is specified.  Has no effect on existing users.
  default: -1
  services:
  - rgw
  with_legacy: true
- name: rgw_multipart_min_part_size
  type: size
  level: advanced
  desc: Minimum S3 multipart-upload part size
  long_desc: When doing a multipart upload, each part (other than the last part) must
    be at least this size.
  default: 5_M
  services:
  - rgw
  with_legacy: true
- name: rgw_multipart_part_upload_limit
  type: int
  level: advanced
  desc: Max number of parts in multipart upload
  default: 10000
  services:
  - rgw
  with_legacy: true
- name: rgw_max_slo_entries
  type: int
  level: advanced
  desc: Max number of entries in Swift Static Large Object manifest
  default: 1000
  services:
  - rgw
  with_legacy: true
- name: rgw_olh_pending_timeout_sec
  type: int
  level: dev
  desc: Max time for pending OLH change to complete
  long_desc: OLH is a versioned object's logical head. Operations on it are journaled
    and as pending before completion. If an operation doesn't complete with this amount
    of seconds, we remove the operation from the journal.
  default: 1_hr
  services:
  - rgw
  with_legacy: true
- name: rgw_user_max_buckets
  type: int
  level: basic
  desc: Max number of buckets per user
  long_desc: A user can create at most this number of buckets. Zero means no limit;
    a negative value means users cannot create any new buckets, although users will
    retain buckets already created.
  default: 1000
  services:
  - rgw
  with_legacy: true
- name: rgw_objexp_gc_interval
  type: uint
  level: advanced
  desc: Swift objects expirer garbage collector interval
  default: 600
  services:
  - rgw
  with_legacy: true
- name: rgw_objexp_hints_num_shards
  type: uint
  level: advanced
  desc: Number of object expirer data shards
  long_desc: The number of shards the (Swift) object expirer will store its data on.
  default: 127
  services:
  - rgw
  with_legacy: true
# maximum number of entries in a single operation when processing objexp data
- name: rgw_objexp_chunk_size
  type: uint
  level: dev
  default: 100
  services:
  - rgw
  with_legacy: true
- name: rgw_enable_static_website
  type: bool
  level: basic
  desc: Enable static website APIs
  long_desc: This configurable controls whether RGW handles the website control APIs.
    RGW can server static websites if s3website hostnames are configured, and unrelated
    to this configurable.
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_user_unique_email
  type: bool
  level: basic
  desc: Require local RGW users to have unique email addresses
  long_desc: Enforce builtin user accounts to have unique email addresses.  This setting
    is historical.  In future, non-enforcement of email address uniqueness is likely
    to become the default.
  default: true
  services:
  - rgw
- name: rgw_log_http_headers
  type: str
  level: basic
  desc: List of HTTP headers to log
  long_desc: A comma delimited list of HTTP headers to log when seen, ignores case
    (e.g., http_x_forwarded_for).
  fmt_desc: Comma-delimited list of HTTP headers to include with ops
    log entries.  Header names are case insensitive, and use
    the full header name with words separated by underscores.
  example: http_x_forwarded_for, http_x_special_k
  services:
  - rgw
  with_legacy: true
- name: rgw_num_async_rados_threads
  type: int
  level: advanced
  desc: Number of concurrent RADOS operations in multisite sync
  long_desc: The number of concurrent RADOS IO operations that will be triggered for
    handling multisite sync operations. This includes control related work, and not
    the actual sync operations.
  default: 32
  services:
  - rgw
  with_legacy: true
- name: rgw_md_notify_interval_msec
  type: int
  level: advanced
  desc: Length of time to aggregate metadata changes
  long_desc: Length of time (in milliseconds) in which the master zone aggregates
    all the metadata changes that occurred, before sending notifications to all the
    other zones.
  default: 200
  services:
  - rgw
  with_legacy: true
- name: rgw_run_sync_thread
  type: bool
  level: advanced
  desc: Should run sync thread
  fmt_desc: If there are other zones in the realm to sync from, spawn threads
    to handle the sync of data and metadata.
  default: true
  services:
  - rgw
  with_legacy: true
- name: rgw_sync_lease_period
  type: int
  level: dev
  default: 2_min
  services:
  - rgw
  with_legacy: true
- name: rgw_sync_log_trim_interval
  type: int
  level: advanced
  desc: Sync log trim interval
  long_desc: Time in seconds between attempts to trim sync logs.
  default: 20_min
  services:
  - rgw
  with_legacy: true
- name: rgw_sync_log_trim_max_buckets
  type: int
  level: advanced
  desc: Maximum number of buckets to trim per interval
  long_desc: The maximum number of buckets to consider for bucket index log trimming
    each trim interval, regardless of the number of bucket index shards. Priority
    is given to buckets with the most sync activity over the last trim interval.
  default: 16
  services:
  - rgw
  see_also:
  - rgw_sync_log_trim_interval
  - rgw_sync_log_trim_min_cold_buckets
  - rgw_sync_log_trim_concurrent_buckets
- name: rgw_sync_log_trim_min_cold_buckets
  type: int
  level: advanced
  desc: Minimum number of cold buckets to trim per interval
  long_desc: Of the `rgw_sync_log_trim_max_buckets` selected for bucket index log
    trimming each trim interval, at least this many of them must be 'cold' buckets.
    These buckets are selected in order from the list of all bucket instances, to
    guarantee that all buckets will be visited eventually.
  default: 4
  services:
  - rgw
  see_also:
  - rgw_sync_log_trim_interval
  - rgw_sync_log_trim_max_buckets
  - rgw_sync_log_trim_concurrent_buckets
- name: rgw_sync_log_trim_concurrent_buckets
  type: int
  level: advanced
  desc: Maximum number of buckets to trim in parallel
  default: 4
  services:
  - rgw
  see_also:
  - rgw_sync_log_trim_interval
  - rgw_sync_log_trim_max_buckets
  - rgw_sync_log_trim_min_cold_buckets
# Dynamic throttling for datalog trim operations
- name: rgw_datalog_trim_dynamic_throttle
  type: bool
  level: advanced
  desc: Enable dynamic throttling for datalog trim operations based on system load
  long_desc: When enabled, datalog trim operations will be throttled based on CPU load,
    memory usage, RADOS operation latency, and pending operations count. This helps
    prevent trim operations from overwhelming the system during high load periods.
  default: false
  services:
  - rgw
  see_also:
  - rgw_datalog_trim_cpu_threshold_low
  - rgw_datalog_trim_cpu_threshold_high
  - rgw_datalog_trim_mem_threshold_low
  - rgw_datalog_trim_mem_threshold_high
- name: rgw_datalog_trim_cpu_threshold_low
  type: float
  level: advanced
  desc: CPU load threshold for low throttling level
  long_desc: CPU load average (normalized by number of cores) below which trim operations
    run at normal speed. Above this threshold, medium throttling is applied.
  default: 0.5
  services:
  - rgw
  see_also:
  - rgw_datalog_trim_dynamic_throttle
  - rgw_datalog_trim_cpu_threshold_high
- name: rgw_datalog_trim_cpu_threshold_high
  type: float
  level: advanced
  desc: CPU load threshold for high throttling level
  long_desc: CPU load average (normalized by number of cores) above which high
    throttling is applied to trim operations.
  default: 0.8
  services:
  - rgw
  see_also:
  - rgw_datalog_trim_dynamic_throttle
  - rgw_datalog_trim_cpu_threshold_low
- name: rgw_datalog_trim_mem_threshold_low
  type: float
  level: advanced
  desc: Memory usage threshold for low throttling level
  long_desc: Memory usage percentage below which trim operations run at normal speed.
    Above this threshold, medium throttling is applied.
  default: 0.6
  services:
  - rgw
  see_also:
  - rgw_datalog_trim_dynamic_throttle
  - rgw_datalog_trim_mem_threshold_high
- name: rgw_datalog_trim_mem_threshold_high
  type: float
  level: advanced
  desc: Memory usage threshold for high throttling level
  long_desc: Memory usage percentage above which high throttling is applied to trim operations.
  default: 0.85
  services:
  - rgw
  see_also:
  - rgw_datalog_trim_dynamic_throttle
  - rgw_datalog_trim_mem_threshold_low
- name: rgw_datalog_trim_sleep_low
  type: uint
  level: advanced
  desc: Sleep time in milliseconds for low load throttling
  long_desc: Time to sleep between trim operations when system load is low.
  default: 10
  services:
  - rgw
  see_also:
  - rgw_datalog_trim_dynamic_throttle
  - rgw_datalog_trim_sleep_medium
  - rgw_datalog_trim_sleep_high
- name: rgw_datalog_trim_sleep_medium
  type: uint
  level: advanced
  desc: Sleep time in milliseconds for medium load throttling
  long_desc: Time to sleep between trim operations when system load is medium.
  default: 50
  services:
  - rgw
  see_also:
  - rgw_datalog_trim_dynamic_throttle
  - rgw_datalog_trim_sleep_low
  - rgw_datalog_trim_sleep_high
- name: rgw_datalog_trim_sleep_high
  type: uint
  level: advanced
  desc: Sleep time in milliseconds for high load throttling
  long_desc: Time to sleep between trim operations when system load is high.
  default: 200
  services:
  - rgw
  see_also:
  - rgw_datalog_trim_dynamic_throttle
  - rgw_datalog_trim_sleep_low
  - rgw_datalog_trim_sleep_medium
- name: rgw_datalog_trim_batch_size_low
  type: uint
  level: advanced
  desc: Batch size for trim operations under low load
  long_desc: Number of operations to process in a batch before applying throttling
    when system load is low.
  default: 1000
  services:
  - rgw
  see_also:
  - rgw_datalog_trim_dynamic_throttle
  - rgw_datalog_trim_batch_size_medium
  - rgw_datalog_trim_batch_size_high
- name: rgw_datalog_trim_batch_size_medium
  type: uint
  level: advanced
  desc: Batch size for trim operations under medium load
  long_desc: Number of operations to process in a batch before applying throttling
    when system load is medium.
  default: 500
  services:
  - rgw
  see_also:
  - rgw_datalog_trim_dynamic_throttle
  - rgw_datalog_trim_batch_size_low
  - rgw_datalog_trim_batch_size_high
- name: rgw_datalog_trim_batch_size_high
  type: uint
  level: advanced
  desc: Batch size for trim operations under high load
  long_desc: Number of operations to process in a batch before applying throttling
    when system load is high.
  default: 100
  services:
  - rgw
  see_also:
  - rgw_datalog_trim_dynamic_throttle
  - rgw_datalog_trim_batch_size_low
  - rgw_datalog_trim_batch_size_medium
# Dynamic throttling for bucket index log trim operations
- name: rgw_bilog_trim_dynamic_throttle
  type: bool
  level: advanced
  desc: Enable dynamic throttling for bucket index log trim operations based on system load
  long_desc: When enabled, bucket index log trim operations will be throttled based on CPU load,
    memory usage, RADOS operation latency, and pending operations count. This helps
    prevent trim operations from overwhelming the system during high load periods.
  default: false
  services:
  - rgw
  see_also:
  - rgw_bilog_trim_cpu_threshold_low
  - rgw_bilog_trim_cpu_threshold_high
  - rgw_bilog_trim_mem_threshold_low
  - rgw_bilog_trim_mem_threshold_high
- name: rgw_bilog_trim_cpu_threshold_low
  type: float
  level: advanced
  desc: CPU load threshold for low throttling level in bucket index log trim
  long_desc: CPU load average (normalized by number of cores) below which bucket index log
    trim operations run at normal speed. Above this threshold, medium throttling is applied.
  default: 0.5
  services:
  - rgw
  see_also:
  - rgw_bilog_trim_dynamic_throttle
  - rgw_bilog_trim_cpu_threshold_high
- name: rgw_bilog_trim_cpu_threshold_high
  type: float
  level: advanced
  desc: CPU load threshold for high throttling level in bucket index log trim
  long_desc: CPU load average (normalized by number of cores) above which high
    throttling is applied to bucket index log trim operations.
  default: 0.8
  services:
  - rgw
  see_also:
  - rgw_bilog_trim_dynamic_throttle
  - rgw_bilog_trim_cpu_threshold_low
- name: rgw_bilog_trim_mem_threshold_low
  type: float
  level: advanced
  desc: Memory usage threshold for low throttling level in bucket index log trim
  long_desc: Memory usage percentage below which bucket index log trim operations run at normal speed.
    Above this threshold, medium throttling is applied.
  default: 0.6
  services:
  - rgw
  see_also:
  - rgw_bilog_trim_dynamic_throttle
  - rgw_bilog_trim_mem_threshold_high
- name: rgw_bilog_trim_mem_threshold_high
  type: float
  level: advanced
  desc: Memory usage threshold for high throttling level in bucket index log trim
  long_desc: Memory usage percentage above which high throttling is applied to bucket index log trim operations.
  default: 0.85
  services:
  - rgw
  see_also:
  - rgw_bilog_trim_dynamic_throttle
  - rgw_bilog_trim_mem_threshold_low
- name: rgw_bilog_trim_sleep_low
  type: uint
  level: advanced
  desc: Sleep time in milliseconds for low load throttling in bucket index log trim
  long_desc: Time to sleep between bucket index log trim operations when system load is low.
  default: 10
  services:
  - rgw
  see_also:
  - rgw_bilog_trim_dynamic_throttle
  - rgw_bilog_trim_sleep_medium
  - rgw_bilog_trim_sleep_high
- name: rgw_bilog_trim_sleep_medium
  type: uint
  level: advanced
  desc: Sleep time in milliseconds for medium load throttling in bucket index log trim
  long_desc: Time to sleep between bucket index log trim operations when system load is medium.
  default: 50
  services:
  - rgw
  see_also:
  - rgw_bilog_trim_dynamic_throttle
  - rgw_bilog_trim_sleep_low
  - rgw_bilog_trim_sleep_high
- name: rgw_bilog_trim_sleep_high
  type: uint
  level: advanced
  desc: Sleep time in milliseconds for high load throttling in bucket index log trim
  long_desc: Time to sleep between bucket index log trim operations when system load is high.
  default: 200
  services:
  - rgw
  see_also:
  - rgw_bilog_trim_dynamic_throttle
  - rgw_bilog_trim_sleep_low
  - rgw_bilog_trim_sleep_medium
- name: rgw_bilog_trim_batch_size_low
  type: uint
  level: advanced
  desc: Batch size for bucket index log trim operations under low load
  long_desc: Number of bucket instances to process in a batch before applying throttling
    when system load is low.
  default: 16
  services:
  - rgw
  see_also:
  - rgw_bilog_trim_dynamic_throttle
  - rgw_bilog_trim_batch_size_medium
  - rgw_bilog_trim_batch_size_high
- name: rgw_bilog_trim_batch_size_medium
  type: uint
  level: advanced
  desc: Batch size for bucket index log trim operations under medium load
  long_desc: Number of bucket instances to process in a batch before applying throttling
    when system load is medium.
  default: 8
  services:
  - rgw
  see_also:
  - rgw_bilog_trim_dynamic_throttle
  - rgw_bilog_trim_batch_size_low
  - rgw_bilog_trim_batch_size_high
- name: rgw_bilog_trim_batch_size_high
  type: uint
  level: advanced
  desc: Batch size for bucket index log trim operations under high load
  long_desc: Number of bucket instances to process in a batch before applying throttling
    when system load is high.
  default: 4
  services:
  - rgw
  see_also:
  - rgw_bilog_trim_dynamic_throttle
  - rgw_bilog_trim_batch_size_low
  - rgw_bilog_trim_batch_size_medium
# Cluster-wide load monitoring and OSD protection
- name: rgw_cluster_load_monitor_enabled
  type: bool
  level: advanced
  desc: Enable cluster-wide load monitoring for trim operations
  long_desc: When enabled, RGW will monitor load across all RGW nodes and OSDs
    in the cluster to make better throttling decisions for trim operations.
  default: false
  services:
  - rgw
  see_also:
  - rgw_cluster_cpu_threshold_low
  - rgw_cluster_cpu_threshold_high
  - rgw_osd_cpu_threshold
- name: rgw_cluster_cpu_threshold_low
  type: float
  level: advanced
  desc: Cluster-wide CPU load threshold for low throttling level
  long_desc: Average CPU load across all RGW nodes below which trim operations
    run at normal speed. Above this threshold, medium throttling is applied.
  default: 0.4
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_cluster_cpu_threshold_high
- name: rgw_cluster_cpu_threshold_high
  type: float
  level: advanced
  desc: Cluster-wide CPU load threshold for high throttling level
  long_desc: Average CPU load across all RGW nodes above which high throttling
    is applied to trim operations.
  default: 0.7
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_cluster_cpu_threshold_low
- name: rgw_cluster_memory_threshold_low
  type: float
  level: advanced
  desc: Cluster-wide memory usage threshold for low throttling level
  long_desc: Average memory usage across all RGW nodes below which trim operations
    run at normal speed. Above this threshold, medium throttling is applied.
  default: 0.5
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_cluster_memory_threshold_high
- name: rgw_cluster_memory_threshold_high
  type: float
  level: advanced
  desc: Cluster-wide memory usage threshold for high throttling level
  long_desc: Average memory usage across all RGW nodes above which high throttling
    is applied to trim operations.
  default: 0.8
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_cluster_memory_threshold_low
- name: rgw_osd_cpu_threshold
  type: float
  level: advanced
  desc: OSD CPU load threshold for trim operation protection
  long_desc: CPU load threshold above which an OSD is considered overloaded
    and trim operations affecting it will be throttled or delayed.
  default: 0.8
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_osd_disk_threshold
  - rgw_osd_max_trim_ops
- name: rgw_osd_disk_threshold
  type: float
  level: advanced
  desc: OSD disk usage threshold for trim operation protection
  long_desc: Disk usage percentage above which an OSD is considered overloaded
    and trim operations affecting it will be throttled or delayed.
  default: 0.9
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_osd_cpu_threshold
  - rgw_osd_max_trim_ops
- name: rgw_osd_max_trim_ops
  type: uint
  level: advanced
  desc: Maximum concurrent trim operations per OSD
  long_desc: Maximum number of trim operations that can run concurrently on a single OSD.
    When this limit is reached, new trim operations will be delayed.
  default: 10
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_osd_cpu_threshold
  - rgw_osd_disk_threshold
- name: rgw_osd_latency_threshold_ms
  type: float
  level: advanced
  desc: OSD operation latency threshold in milliseconds
  long_desc: Average operation latency above which an OSD is considered overloaded
    and trim operations affecting it will be throttled.
  default: 100.0
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_osd_cpu_threshold
- name: rgw_cluster_trim_sleep_low
  type: uint
  level: advanced
  desc: Sleep time for cluster-wide low load throttling
  long_desc: Time to sleep between trim operations when cluster load is low.
  default: 10
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_cluster_trim_sleep_medium
  - rgw_cluster_trim_sleep_high
- name: rgw_cluster_trim_sleep_medium
  type: uint
  level: advanced
  desc: Sleep time for cluster-wide medium load throttling
  long_desc: Time to sleep between trim operations when cluster load is medium.
  default: 50
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_cluster_trim_sleep_low
  - rgw_cluster_trim_sleep_high
- name: rgw_cluster_trim_sleep_high
  type: uint
  level: advanced
  desc: Sleep time for cluster-wide high load throttling
  long_desc: Time to sleep between trim operations when cluster load is high.
  default: 200
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_cluster_trim_sleep_low
  - rgw_cluster_trim_sleep_medium
- name: rgw_cluster_trim_sleep_critical
  type: uint
  level: advanced
  desc: Sleep time for cluster-wide critical load throttling
  long_desc: Time to sleep between trim operations when cluster load is critical.
  default: 500
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_cluster_trim_sleep_high
- name: rgw_cluster_trim_batch_size_low
  type: uint
  level: advanced
  desc: Batch size for cluster-wide low load operations
  long_desc: Number of operations to process in a batch when cluster load is low.
  default: 1000
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_cluster_trim_batch_size_medium
  - rgw_cluster_trim_batch_size_high
- name: rgw_cluster_trim_batch_size_medium
  type: uint
  level: advanced
  desc: Batch size for cluster-wide medium load operations
  long_desc: Number of operations to process in a batch when cluster load is medium.
  default: 500
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_cluster_trim_batch_size_low
  - rgw_cluster_trim_batch_size_high
- name: rgw_cluster_trim_batch_size_high
  type: uint
  level: advanced
  desc: Batch size for cluster-wide high load operations
  long_desc: Number of operations to process in a batch when cluster load is high.
  default: 100
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_cluster_trim_batch_size_low
  - rgw_cluster_trim_batch_size_medium
- name: rgw_cluster_trim_batch_size_critical
  type: uint
  level: advanced
  desc: Batch size for cluster-wide critical load operations
  long_desc: Number of operations to process in a batch when cluster load is critical.
  default: 10
  services:
  - rgw
  see_also:
  - rgw_cluster_load_monitor_enabled
  - rgw_cluster_trim_batch_size_high
- name: rgw_sync_data_inject_err_probability
  type: float
  level: dev
  default: 0
  services:
  - rgw
  with_legacy: true
- name: rgw_sync_meta_inject_err_probability
  type: float
  level: dev
  default: 0
  services:
  - rgw
  with_legacy: true
- name: rgw_sync_trace_history_size
  type: size
  level: advanced
  desc: Sync trace history size
  long_desc: Maximum number of complete sync trace entries to keep.
  default: 4_K
  services:
  - rgw
  with_legacy: true
- name: rgw_sync_trace_per_node_log_size
  type: int
  level: advanced
  desc: Sync trace per-node log size
  long_desc: The number of log entries to keep per sync-trace node.
  default: 32
  services:
  - rgw
  with_legacy: true
- name: rgw_sync_trace_servicemap_update_interval
  type: int
  level: advanced
  desc: Sync-trace service-map update interval
  long_desc: Number of seconds between service-map updates of sync-trace events.
  default: 10
  services:
  - rgw
  with_legacy: true
- name: rgw_period_push_interval
  type: float
  level: advanced
  desc: Period push interval
  long_desc: Number of seconds to wait before retrying 'period push' operation.
  default: 2
  services:
  - rgw
  with_legacy: true
- name: rgw_period_push_interval_max
  type: float
  level: advanced
  desc: Period push maximum interval
  long_desc: The max number of seconds to wait before retrying 'period push' after
    exponential backoff.
  default: 30
  services:
  - rgw
  with_legacy: true
- name: rgw_safe_max_objects_per_shard
  type: int
  level: advanced
  desc: Safe number of objects per shard
  long_desc: This is the max number of objects per bucket index shard that RGW considers
    safe. RGW will warn if it identifies a bucket where its per-shard count is higher
    than a percentage of this number.
  default: 102400
  services:
  - rgw
  see_also:
  - rgw_shard_warning_threshold
  with_legacy: true
# pct of safe max at which to warn
- name: rgw_shard_warning_threshold
  type: float
  level: advanced
  desc: Warn about max objects per shard
  long_desc: Warn if number of objects per shard in a specific bucket passed this
    percentage of the safe number.
  default: 90
  services:
  - rgw
  see_also:
  - rgw_safe_max_objects_per_shard
  with_legacy: true
- name: rgw_swift_versioning_enabled
  type: bool
  level: advanced
  desc: Enable Swift versioning
  fmt_desc: |
    Enables the Object Versioning of OpenStack Object Storage API.
    This allows clients to put the ``X-Versions-Location`` attribute
    on containers that should be versioned. The attribute specifies
    the name of container storing archived versions. It must be owned
    by the same user that the versioned container due to access
    control verification - ACLs are NOT taken into consideration.
    Those containers cannot be versioned by the S3 object versioning
    mechanism.

    A slightly different attribute, ``X-History-Location``, which is also understood by
    `OpenStack Swift <https://docs.openstack.org/swift/latest/api/object_versioning.html>`_
    for handling ``DELETE`` operations, is currently not supported.
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_swift_custom_header
  type: str
  level: advanced
  desc: Enable swift custom header
  long_desc: If not empty, specifies a name of HTTP header that can include custom
    data. When uploading an object, if this header is passed RGW will store this header
    info and it will be available when listing the bucket.
  services:
  - rgw
  with_legacy: true
- name: rgw_swift_need_stats
  type: bool
  level: advanced
  desc: Enable stats on bucket listing in Swift
  default: true
  services:
  - rgw
  with_legacy: true
- name: rgw_reshard_num_logs
  type: uint
  level: advanced
  default: 16
  services:
  - rgw
  - rgw
  min: 1
- name: rgw_reshard_bucket_lock_duration
  type: uint
  level: advanced
  desc: Number of seconds the timeout on the reshard locks (bucket reshard lock and
    reshard log lock) are set to. As a reshard proceeds these locks can be renewed/extended.
    If too short, reshards cannot complete and will fail, causing a future reshard
    attempt. If too long a hung or crashed reshard attempt will keep the bucket locked
    for an extended period, not allowing RGW to detect the failed reshard attempt
    and recover.
  default: 360
  tags:
  - performance
  services:
  - rgw
  - rgw
  min: 30
- name: rgw_reshard_batch_size
  type: uint
  level: advanced
  desc: Number of reshard entries to batch together before sending the operations
    to the CLS back-end
  default: 64
  tags:
  - performance
  services:
  - rgw
  - rgw
  min: 8
- name: rgw_reshard_max_aio
  type: uint
  level: advanced
  desc: Maximum number of outstanding asynchronous I/O operations to allow at a time
    during resharding
  default: 128
  tags:
  - performance
  services:
  - rgw
  - rgw
  min: 16
- name: rgw_trust_forwarded_https
  type: bool
  level: advanced
  desc: Trust Forwarded and X-Forwarded-Proto headers
  long_desc: When a proxy in front of radosgw is used for ssl termination, radosgw
    does not know whether incoming http connections are secure. Enable this option
    to trust the Forwarded and X-Forwarded-Proto headers sent by the proxy when determining
    whether the connection is secure. This is required for some features, such as
    server side encryption. (Never enable this setting if you do not have a trusted
    proxy in front of radosgw, or else malicious users will be able to set these headers
    in any request.)
  fmt_desc: When a proxy in front of radosgw is used for ssl termination, radosgw
    does not know whether incoming http connections are secure. Enable
    this option to trust the ``Forwarded`` and ``X-Forwarded-Proto`` headers
    sent by the proxy when determining whether the connection is secure.
    This is required for some features, such as server side encryption.
    (Never enable this setting if you do not have a trusted proxy in front of
    radosgw, or else malicious users will be able to set these headers in
    any request.)
  default: false
  services:
  - rgw
  see_also:
  - rgw_crypt_require_ssl
  with_legacy: true
- name: rgw_crypt_require_ssl
  type: bool
  level: advanced
  desc: Requests including encryption key headers must be sent over ssl
  default: true
  services:
  - rgw
  with_legacy: true
# base64 encoded key for encryption of rgw objects
- name: rgw_crypt_default_encryption_key
  type: str
  level: dev
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_s3_kms_backend
  type: str
  level: advanced
  desc: Where the SSE-KMS encryption keys are stored. Supported KMS systems are OpenStack
    Barbican ('barbican', the default) and HashiCorp Vault ('vault').
  fmt_desc: Where the SSE-KMS encryption keys are stored. Supported KMS
    systems are OpenStack Barbican (``barbican``, the default) and
    HashiCorp Vault (``vault``).
  default: barbican
  services:
  - rgw
  enum_values:
  - barbican
  - vault
  - testing
  - kmip
  with_legacy: true
# extra keys that may be used for aws:kms
# defined as map "key1=YmluCmJvb3N0CmJvb3N0LQ== key2=b3V0CnNyYwpUZXN0aW5nCg=="
- name: rgw_crypt_s3_kms_encryption_keys
  type: str
  level: dev
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_vault_auth
  type: str
  level: advanced
  desc: Type of authentication method to be used with Vault.
  fmt_desc: Type of authentication method to be used. The only method
    currently supported is ``token``.
  default: token
  services:
  - rgw
  see_also:
  - rgw_crypt_s3_kms_backend
  - rgw_crypt_vault_addr
  - rgw_crypt_vault_token_file
  enum_values:
  - token
  - agent
  with_legacy: true
- name: rgw_crypt_vault_token_file
  type: str
  level: advanced
  desc: If authentication method is 'token', provide a path to the token file, which
    for security reasons should readable only by Rados Gateway.
  services:
  - rgw
  see_also:
  - rgw_crypt_s3_kms_backend
  - rgw_crypt_vault_auth
  - rgw_crypt_vault_addr
  with_legacy: true
- name: rgw_crypt_vault_addr
  type: str
  level: advanced
  desc: Vault server base address.
  fmt_desc: Vault server base address, e.g. ``http://vaultserver:8200``.
  services:
  - rgw
  see_also:
  - rgw_crypt_s3_kms_backend
  - rgw_crypt_vault_auth
  - rgw_crypt_vault_prefix
  with_legacy: true
# Optional URL prefix to Vault secret path
- name: rgw_crypt_vault_prefix
  type: str
  level: advanced
  desc: Vault secret URL prefix, which can be used to restrict access to a particular
    subset of the Vault secret space.
  fmt_desc: The Vault secret URL prefix, which can be used to restrict access
    to a particular subset of the secret space, e.g. ``/v1/secret/data``.
  services:
  - rgw
  see_also:
  - rgw_crypt_s3_kms_backend
  - rgw_crypt_vault_addr
  - rgw_crypt_vault_auth
  with_legacy: true
# kv, transit or other supported secret engines
- name: rgw_crypt_vault_secret_engine
  type: str
  level: advanced
  desc: Vault Secret Engine to be used to retrieve encryption keys.
  fmt_desc: |
    Vault Secret Engine to be used to retrieve encryption keys: choose
    between kv-v2, transit.
  default: transit
  services:
  - rgw
  see_also:
  - rgw_crypt_s3_kms_backend
  - rgw_crypt_vault_auth
  - rgw_crypt_vault_addr
  with_legacy: true
#  Vault Namespace (only availabe in Vault Enterprise Version)
- name: rgw_crypt_vault_namespace
  type: str
  level: advanced
  desc: Vault Namespace to be used to select your tenant
  fmt_desc: If set, Vault Namespace provides tenant isolation for teams and individuals
    on the same Vault Enterprise instance, e.g. ``acme/tenant1``
  services:
  - rgw
  see_also:
  - rgw_crypt_s3_kms_backend
  - rgw_crypt_vault_auth
  - rgw_crypt_vault_addr
  with_legacy: true
# Enable TLS authentication rgw and vault
- name: rgw_crypt_vault_verify_ssl
  type: bool
  level: advanced
  desc: Should RGW verify the vault server SSL certificate.
  default: true
  services:
  - rgw
  with_legacy: true
# TLS certs options
- name: rgw_crypt_vault_ssl_cacert
  type: str
  level: advanced
  desc: Path for custom ca certificate for accessing vault server
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_vault_ssl_clientcert
  type: str
  level: advanced
  desc: Path for custom client certificate for accessing vault server
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_vault_ssl_clientkey
  type: str
  level: advanced
  desc: Path for private key required for client cert
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_kmip_addr
  type: str
  level: advanced
  desc: kmip server address
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_kmip_ca_path
  type: str
  level: advanced
  desc: ca for kmip servers
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_kmip_username
  type: str
  level: advanced
  desc: when authenticating via username
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_kmip_password
  type: str
  level: advanced
  desc: optional w/ username
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_kmip_client_cert
  type: str
  level: advanced
  desc: connect using client certificate
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_kmip_client_key
  type: str
  level: advanced
  desc: connect using client certificate
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_kmip_kms_key_template
  type: str
  level: advanced
  desc: sse-kms; kmip key names
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_kmip_s3_key_template
  type: str
  level: advanced
  desc: sse-s3; kmip key template
  default: $keyid
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_suppress_logs
  type: bool
  level: advanced
  desc: Suppress logs that might print client key
  default: true
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_sse_s3_backend
  type: str
  level: advanced
  desc: Where the SSE-S3 encryption keys are stored. The only valid choice here is
    HashiCorp Vault ('vault').
  fmt_desc: Where the SSE-S3 encryption keys are stored. The only valid
    choice is HashiCorp Vault (``vault``).
  default: vault
  services:
  - rgw
  enum_values:
  - vault
  with_legacy: true

- name: rgw_crypt_sse_s3_vault_secret_engine
  type: str
  level: advanced
  desc: Vault Secret Engine to be used to retrieve encryption keys.
  fmt_desc: |
    Vault Secret Engine to be used to retrieve encryption keys.  The
    only valid choice here is transit.
  default: transit
  services:
  - rgw
  see_also:
  - rgw_crypt_sse_s3_backend
  - rgw_crypt_sse_s3_vault_auth
  - rgw_crypt_sse_s3_vault_addr
  with_legacy: true
- name: rgw_crypt_sse_s3_key_template
  type: str
  level: advanced
  desc: template for per-bucket sse-s3 keys in vault.
  long_desc: This is the template for per-bucket sse-s3 keys.
    This string may include ``%bucket_id`` which will be expanded out to
    the bucket marker, a unique uuid assigned to that bucket.
    It could contain ``%owner_id``, which will expand out to the owner's id.
    Any other use of % is reserved and should not be used.
    If the template contains ``%bucket_id``, associated bucket keys
    will be automatically removed when the bucket is removed.
  services:
  - rgw
  default: "%bucket_id"
  see_also:
  - rgw_crypt_sse_s3_backend
  - rgw_crypt_sse_s3_vault_auth
  - rgw_crypt_sse_s3_vault_addr
  with_legacy: true
- name: rgw_crypt_sse_s3_vault_auth
  type: str
  level: advanced
  desc: Type of authentication method to be used with SSE-S3 and Vault.
  fmt_desc: Type of authentication method to be used. The only method
    currently supported is ``token``.
  default: token
  services:
  - rgw
  see_also:
  - rgw_crypt_sse_s3_backend
  - rgw_crypt_sse_s3_vault_addr
  - rgw_crypt_sse_s3_vault_token_file
  enum_values:
  - token
  - agent
  with_legacy: true
- name: rgw_crypt_sse_s3_vault_token_file
  type: str
  level: advanced
  desc: If authentication method is 'token', provide a path to the token file, which
    for security reasons should readable only by Rados Gateway.
  services:
  - rgw
  see_also:
  - rgw_crypt_sse_s3_backend
  - rgw_crypt_sse_s3_vault_auth
  - rgw_crypt_sse_s3_vault_addr
  with_legacy: true
- name: rgw_crypt_sse_s3_vault_addr
  type: str
  level: advanced
  desc: SSE-S3 Vault server base address.
  fmt_desc: Vault server base address, e.g. ``http://vaultserver:8200``.
  services:
  - rgw
  see_also:
  - rgw_crypt_sse_s3_backend
  - rgw_crypt_sse_s3_vault_auth
  - rgw_crypt_sse_s3_vault_prefix
  with_legacy: true
# Optional URL prefix to Vault secret path
- name: rgw_crypt_sse_s3_vault_prefix
  type: str
  level: advanced
  desc: SSE-S3 Vault secret URL prefix, which can be used to restrict access to a particular
    subset of the Vault secret space.
  fmt_desc: The Vault secret URL prefix, which can be used to restrict access
    to a particular subset of the secret space, e.g. ``/v1/secret/data``.
  services:
  - rgw
  see_also:
  - rgw_crypt_sse_s3_backend
  - rgw_crypt_sse_s3_vault_addr
  - rgw_crypt_sse_s3_vault_auth
  with_legacy: true
#  Vault Namespace (only availabe in Vault Enterprise Version)
- name: rgw_crypt_sse_s3_vault_namespace
  type: str
  level: advanced
  desc: Vault Namespace to be used to select your tenant
  fmt_desc: If set, Vault Namespace provides tenant isolation for teams and individuals
    on the same Vault Enterprise instance, e.g. ``acme/tenant1``
  services:
  - rgw
  see_also:
  - rgw_crypt_sse_s3_backend
  - rgw_crypt_sse_s3_vault_auth
  - rgw_crypt_sse_s3_vault_addr
  with_legacy: true
# Enable TLS authentication rgw and vault
- name: rgw_crypt_sse_s3_vault_verify_ssl
  type: bool
  level: advanced
  desc: Should RGW verify the vault server SSL certificate.
  default: true
  services:
  - rgw
  with_legacy: true
# TLS certs options
- name: rgw_crypt_sse_s3_vault_ssl_cacert
  type: str
  level: advanced
  desc: Path for custom ca certificate for accessing vault server
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_sse_s3_vault_ssl_clientcert
  type: str
  level: advanced
  desc: Path for custom client certificate for accessing vault server
  services:
  - rgw
  with_legacy: true
- name: rgw_crypt_sse_s3_vault_ssl_clientkey
  type: str
  level: advanced
  desc: Path for private key required for client cert
  services:
  - rgw
  with_legacy: true
- name: rgw_list_bucket_min_readahead
  type: int
  level: advanced
  desc: Minimum number of entries to request from rados for bucket listing
  default: 1000
  services:
  - rgw
  with_legacy: true
- name: rgw_rest_getusage_op_compat
  type: bool
  level: advanced
  desc: REST GetUsage request backward compatibility
  default: false
  services:
  - rgw
  with_legacy: true
# The following are tunables for torrent data
- name: rgw_torrent_flag
  type: bool
  level: advanced
  desc: When true, uploaded objects will calculate and store a SHA256 hash of object
    data so the object can be retrieved as a torrent file
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_torrent_tracker
  type: str
  level: advanced
  desc: Torrent field announce and announce list
  services:
  - rgw
  with_legacy: true
- name: rgw_torrent_createby
  type: str
  level: advanced
  desc: torrent field created by
  services:
  - rgw
  with_legacy: true
- name: rgw_torrent_comment
  type: str
  level: advanced
  desc: Torrent field comment
  services:
  - rgw
  with_legacy: true
- name: rgw_torrent_encoding
  type: str
  level: advanced
  desc: torrent field encoding
  services:
  - rgw
  with_legacy: true
- name: rgw_data_notify_interval_msec
  type: int
  level: advanced
  desc: data changes notification interval to followers
  long_desc: In multisite, radosgw will occasionally broadcast new entries in its
    data changes log to peer zones, so they can prioritize sync of some
    of the most recent changes. Can be disabled with 0.
  default: 200
  services:
  - rgw
  with_legacy: true
- name: rgw_torrent_origin
  type: str
  level: advanced
  desc: Torrent origin
  services:
  - rgw
  with_legacy: true
- name: rgw_torrent_sha_unit
  type: size
  level: advanced
  default: 512_K
  services:
  - rgw
  with_legacy: true
- name: rgw_dynamic_resharding
  type: bool
  level: basic
  desc: Enable dynamic resharding
  long_desc: If true, RGW will dynamically increase the number of shards in buckets
    that have a high number of objects per shard.
  default: true
  services:
  - rgw
  see_also:
  - rgw_max_objs_per_shard
  - rgw_max_dynamic_shards
- name: rgw_max_objs_per_shard
  type: uint
  level: basic
  desc: Max objects per shard for dynamic resharding
  long_desc: This is the max number of objects per bucket index shard that RGW will
    allow with dynamic resharding. RGW will trigger an automatic reshard operation
    on the bucket if it exceeds this number.
  default: 100000
  services:
  - rgw
  see_also:
  - rgw_dynamic_resharding
  - rgw_max_dynamic_shards
- name: rgw_max_dynamic_shards
  type: uint
  level: advanced
  desc: Max shards that dynamic resharding can create
  long_desc: This is the maximum number of bucket index shards that dynamic sharding
    is able to create on its own. This does not limit user requested resharding. Ideally
    this value is a prime number.
  default: 1999
  services:
  - rgw
  see_also:
  - rgw_dynamic_resharding
  - rgw_max_objs_per_shard
  min: 1
- name: rgw_reshard_thread_interval
  type: uint
  level: advanced
  desc: Number of seconds between processing of reshard log entries
  default: 600
  services:
  - rgw
  min: 10
- name: rgw_cache_expiry_interval
  type: uint
  level: advanced
  desc: Number of seconds before entries in the cache are assumed stale and re-fetched.
    Zero is never.
  long_desc: The Rados Gateway stores metadata and objects in an internal cache. This
    should be kept consistent by the OSD's relaying notify events between multiple
    watching RGW processes. In the event that this notification protocol fails, bounding
    the length of time that any data in the cache will be assumed valid will ensure
    that any RGW instance that falls out of sync will eventually recover. This seems
    to be an issue mostly for large numbers of RGW instances under heavy use. If you
    would like to turn off cache expiry, set this value to zero.
  default: 900
  tags:
  - performance
  services:
  - rgw
  - rgw
- name: rgw_inject_notify_timeout_probability
  type: float
  level: dev
  desc: Likelihood of ignoring a notify
  long_desc: This is the probability that the RGW cache will ignore a cache notify
    message. It exists to help with the development and testing of cache consistency
    and recovery improvements. Please do not set it in a production cluster, as it
    actively causes failures. Set this to a floating point value between 0 and 1.
  default: 0
  tags:
  - fault injection
  - testing
  services:
  - rgw
  - rgw
  min: 0
  max: 1
- name: rgw_max_notify_retries
  type: uint
  level: advanced
  desc: Number of attempts to notify peers before giving up.
  long_desc: The number of times we will attempt to update a peer's cache in the event
    of error before giving up. This is unlikely to be an issue unless your cluster
    is very heavily loaded. Beware that increasing this value may cause some operations
    to take longer in exceptional cases and thus may, rarely, cause clients to time
    out.
  default: 3
  tags:
  - error recovery
  services:
  - rgw
  - rgw
- name: rgw_sts_entry
  type: str
  level: advanced
  desc: STS URL prefix
  long_desc: URL path prefix for internal STS requests.
  default: sts
  services:
  - rgw
  with_legacy: true
- name: rgw_sts_key
  type: str
  level: advanced
  desc: STS Key
  long_desc: Key used for encrypting/ decrypting session token.
  default: sts
  services:
  - rgw
  with_legacy: true
# should we try to use sts for s3?
- name: rgw_s3_auth_use_sts
  type: bool
  level: advanced
  desc: Should S3 authentication use STS.
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_sts_max_session_duration
  type: uint
  level: advanced
  desc: Session token max duration
  long_desc: Max duration in seconds for which the session token is valid.
  default: 43200
  services:
  - rgw
  with_legacy: true
- name: rgw_sts_min_session_duration
  type: uint
  level: advanced
  desc: Minimum allowed duration of a session
  default: 900
  services:
  - rgw
  with_legacy: true
- name: rgw_max_listing_results
  type: uint
  level: advanced
  desc: Upper bound on results in listing operations, ListBucket max-keys
  long_desc: This caps the maximum permitted value for listing-like operations in
    RGW S3. Affects ListBucket(max-keys), ListBucketVersions(max-keys), ListBucketMultipartUploads(max-uploads),
    ListMultipartUploadParts(max-parts)
  default: 1000
  services:
  - rgw
  - rgw
  min: 1
  max: 100000
- name: rgw_sts_token_introspection_url
  type: str
  level: advanced
  desc: STS Web Token introspection URL
  long_desc: URL for introspecting an STS Web Token.
  services:
  - rgw
  with_legacy: true
- name: rgw_sts_client_id
  type: str
  level: advanced
  desc: Client Id
  long_desc: Client Id needed for introspecting a Web Token.
  services:
  - rgw
  with_legacy: true
- name: rgw_sts_client_secret
  type: str
  level: advanced
  desc: Client Secret
  long_desc: Client Secret needed for introspecting a Web Token.
  services:
  - rgw
  with_legacy: true
- name: rgw_max_concurrent_requests
  type: int
  level: basic
  desc: Maximum number of concurrent HTTP requests.
  long_desc: Maximum number of concurrent HTTP requests that the beast frontend will
    process. Tuning this can help to limit memory usage under heavy load.
  default: 1024
  tags:
  - performance
  services:
  - rgw
  see_also:
  - rgw_frontends
- name: rgw_scheduler_type
  type: str
  level: advanced
  desc: Set the type of dmclock scheduler, defaults to throttler Other valid values
    are dmclock which is experimental
  fmt_desc: |
    The RGW scheduler to use. Valid values are ``throttler` and
    ``dmclock``. Currently defaults to ``throttler`` which throttles Beast
    frontend requests. ``dmclock` is *experimental* and requires the
    ``dmclock`` to be included in the ``experimental_feature_enabled``
    configuration option.

    The options below tune the experimental dmclock scheduler. For
    additional reading on dmclock, see :ref:`dmclock-qos`. `op_class` for the flags below is
    one of ``admin``, ``auth``, ``metadata``, or ``data``.
  default: throttler
  services:
  - rgw
- name: rgw_dmclock_admin_res
  type: float
  level: advanced
  desc: mclock reservation for admin requests
  default: 100
  services:
  - rgw
  see_also:
  - rgw_dmclock_admin_wgt
  - rgw_dmclock_admin_lim
- name: rgw_dmclock_admin_wgt
  type: float
  level: advanced
  desc: mclock weight for admin requests
  default: 100
  services:
  - rgw
  see_also:
  - rgw_dmclock_admin_res
  - rgw_dmclock_admin_lim
- name: rgw_dmclock_admin_lim
  type: float
  level: advanced
  desc: mclock limit for admin requests
  default: 0
  services:
  - rgw
  see_also:
  - rgw_dmclock_admin_res
  - rgw_dmclock_admin_wgt
- name: rgw_dmclock_auth_res
  type: float
  level: advanced
  desc: mclock reservation for object data requests
  default: 200
  services:
  - rgw
  see_also:
  - rgw_dmclock_auth_wgt
  - rgw_dmclock_auth_lim
- name: rgw_dmclock_auth_wgt
  type: float
  level: advanced
  desc: mclock weight for object data requests
  default: 100
  services:
  - rgw
  see_also:
  - rgw_dmclock_auth_res
  - rgw_dmclock_auth_lim
- name: rgw_dmclock_auth_lim
  type: float
  level: advanced
  desc: mclock limit for object data requests
  default: 0
  services:
  - rgw
  see_also:
  - rgw_dmclock_auth_res
  - rgw_dmclock_auth_wgt
- name: rgw_dmclock_data_res
  type: float
  level: advanced
  desc: mclock reservation for object data requests
  default: 500
  services:
  - rgw
  see_also:
  - rgw_dmclock_data_wgt
  - rgw_dmclock_data_lim
- name: rgw_dmclock_data_wgt
  type: float
  level: advanced
  desc: mclock weight for object data requests
  default: 500
  services:
  - rgw
  see_also:
  - rgw_dmclock_data_res
  - rgw_dmclock_data_lim
- name: rgw_dmclock_data_lim
  type: float
  level: advanced
  desc: mclock limit for object data requests
  default: 0
  services:
  - rgw
  see_also:
  - rgw_dmclock_data_res
  - rgw_dmclock_data_wgt
- name: rgw_dmclock_metadata_res
  type: float
  level: advanced
  desc: mclock reservation for metadata requests
  default: 500
  services:
  - rgw
  see_also:
  - rgw_dmclock_metadata_wgt
  - rgw_dmclock_metadata_lim
- name: rgw_dmclock_metadata_wgt
  type: float
  level: advanced
  desc: mclock weight for metadata requests
  default: 500
  services:
  - rgw
  see_also:
  - rgw_dmclock_metadata_res
  - rgw_dmclock_metadata_lim
- name: rgw_dmclock_metadata_lim
  type: float
  level: advanced
  desc: mclock limit for metadata requests
  default: 0
  services:
  - rgw
  see_also:
  - rgw_dmclock_metadata_res
  - rgw_dmclock_metadata_wgt
- name: rgw_default_data_log_backing
  type: str
  level: advanced
  desc: Default backing store for the RGW data sync log
  long_desc: Whether to use the older OMAP backing store or the high performance FIFO
    based backing store by default. This only covers the creation of the log on startup
    if none exists.
  default: fifo
  services:
  - rgw
  enum_values:
  - fifo
  - omap
- name: rgw_d3n_l1_local_datacache_enabled
  type: bool
  level: advanced
  desc: Enable datacenter-scale dataset delivery local cache
  default: false
  services:
  - rgw
  with_legacy: true
- name: rgw_d3n_l1_datacache_persistent_path
  type: str
  level: advanced
  desc: path for the directory for storing the local cache objects data
  default: /tmp/rgw_datacache/
  services:
  - rgw
  with_legacy: true
- name: rgw_d3n_l1_datacache_size
  type: size
  level: advanced
  desc: datacache maximum size on disk in bytes
  default: 1_G
  services:
  - rgw
  with_legacy: true
- name: rgw_d3n_l1_evict_cache_on_start
  type: bool
  level: advanced
  desc: clear the content of the persistent data cache directory on start
  default: true
  services:
  - rgw
  with_legacy: true
- name: rgw_d3n_l1_fadvise
  type: int
  level: advanced
  desc: posix_fadvise() flag for access pattern of cache files
  long_desc: for example to bypass the page-cache -
    POSIX_FADV_DONTNEED=4
  default: 4
  services:
  - rgw
  with_legacy: true
- name: rgw_d3n_l1_eviction_policy
  type: str
  level: advanced
  desc: select the d3n cache eviction policy
  default: lru
  services:
  - rgw
  enum_values:
  - lru
  - random
  with_legacy: true
- name: rgw_d3n_libaio_aio_threads
  type: int
  level: advanced
  desc: specifies the maximum number of worker threads that may be used by libaio
  default: 20
  services:
  - rgw
  see_also:
  - rgw_thread_pool_size
  with_legacy: true
- name: rgw_d3n_libaio_aio_num
  type: int
  level: advanced
  desc: specifies the maximum number of simultaneous I/O requests that libaio expects to enqueue
  default: 64
  services:
  - rgw
  see_also:
  - rgw_thread_pool_size
  with_legacy: true
- name: rgw_backend_store
  type: str
  level: advanced
  desc: experimental Option to set backend store type
  long_desc: defaults to rados. Other valid values are dbstore(experimental).
  default: rados
  services:
  - rgw
  enum_values:
  - rados
  - dbstore
- name: rgw_luarocks_location
  type: str
  level: advanced
  desc: Directory where luarocks install packages from allowlist
  default: @rgw_luarocks_location@
  services:
  - rgw
  flags:
  - startup
