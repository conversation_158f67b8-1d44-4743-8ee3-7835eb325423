// -*- mode:C++; tab-width:8; c-basic-offset:2; indent-tabs-mode:t -*-
// vim: ts=8 sw=2 smarttab ft=cpp

#pragma once

#include <string>
#include <vector>
#include <memory>
#include <atomic>
#include <chrono>

#include "common/dout.h"
#include "common/ceph_mutex.h"

class RGWCoroutine;
class RGWRados;
class RGWHTTPManager;
class utime_t;
class CephContext;
namespace rgw { namespace sal {
  class RadosStore;
} }

/// Load monitor for DataLog trim operations
class RGWDataLogLoadMonitor {
private:
  CephContext* cct;
  mutable ceph::mutex lock = ceph::make_mutex("RGWDataLogLoadMonitor::lock");

  // Load metrics
  double current_cpu_load = 0.0;
  double current_memory_usage = 0.0;
  std::chrono::steady_clock::time_point last_update = std::chrono::steady_clock::time_point::min();

  // Configuration
  struct Config {
    double cpu_threshold_low = 0.5;
    double cpu_threshold_high = 0.8;
    double mem_threshold_low = 0.6;
    double mem_threshold_high = 0.85;

    uint32_t sleep_ms_low = 10;
    uint32_t sleep_ms_medium = 50;
    uint32_t sleep_ms_high = 200;

    uint32_t batch_size_low = 1000;
    uint32_t batch_size_medium = 500;
    uint32_t batch_size_high = 100;

    uint32_t update_interval_sec = 5;
  } config;

  void update_config();

public:
  explicit RGWDataLogLoadMonitor(CephContext* _cct);
  ~RGWDataLogLoadMonitor() = default;

  enum class LoadLevel {
    LOW = 0,
    MEDIUM = 1,
    HIGH = 2
  };

  void update_load_metrics();
  LoadLevel get_current_load_level();
  uint32_t get_sleep_time_ms(LoadLevel level) const;
  uint32_t get_batch_size(LoadLevel level) const;
  bool should_throttle();

  void dump_metrics(const DoutPrefixProvider *dpp) const;
};

// DataLogTrimCR factory function
extern RGWCoroutine* create_data_log_trim_cr(const DoutPrefixProvider *dpp, rgw::sal::RadosStore* store,
                                             RGWHTTPManager *http,
                                             int num_shards, utime_t interval);

// factory function for datalog trim via radosgw-admin
RGWCoroutine* create_admin_data_log_trim_cr(const DoutPrefixProvider *dpp, rgw::sal::RadosStore* store,
                                            RGWHTTPManager *http,
                                            int num_shards,
                                            std::vector<std::string>& markers);
