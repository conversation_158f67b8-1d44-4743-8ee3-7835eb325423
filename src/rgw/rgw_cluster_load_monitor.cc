// -*- mode:C++; tab-width:8; c-basic-offset:2; indent-tabs-mode:t -*-
// vim: ts=8 sw=2 smarttab ft=cpp

#include "rgw_cluster_load_monitor.h"

#include <fstream>
#include <unistd.h>
#include <thread>
#include <chrono>

#include "common/debug.h"
#include "common/errno.h"
#include "common/Formatter.h"
#include "rgw_sal_rados.h"
#include "rgw_zone.h"
#include "services/svc_rados.h"

#define dout_subsys ceph_subsys_rgw

using namespace std;

namespace rgw {

// Encoding/decoding for metrics structures
void ClusterLoadMetrics::RGWNodeMetrics::encode(bufferlist& bl) const {
  ENCODE_START(1, 1, bl);
  encode(node_id, bl);
  encode(cpu_load, bl);
  encode(memory_usage, bl);
  encode(active_trim_operations, bl);
  encode(last_update, bl);
  ENCODE_FINISH(bl);
}

void ClusterLoadMetrics::RGWNodeMetrics::decode(bufferlist::const_iterator& p) {
  DECODE_START(1, p);
  decode(node_id, p);
  decode(cpu_load, p);
  decode(memory_usage, p);
  decode(active_trim_operations, p);
  decode(last_update, p);
  DECODE_FINISH(p);
}

void ClusterLoadMetrics::OSDMetrics::encode(bufferlist& bl) const {
  ENCODE_START(1, 1, bl);
  encode(osd_id, bl);
  encode(cpu_load, bl);
  encode(disk_usage, bl);
  encode(pending_ops, bl);
  encode(trim_ops_in_progress, bl);
  encode(avg_op_latency_ms, bl);
  encode(last_update, bl);
  ENCODE_FINISH(bl);
}

void ClusterLoadMetrics::OSDMetrics::decode(bufferlist::const_iterator& p) {
  DECODE_START(1, p);
  decode(osd_id, p);
  decode(cpu_load, p);
  decode(disk_usage, p);
  decode(pending_ops, p);
  decode(trim_ops_in_progress, p);
  decode(avg_op_latency_ms, p);
  decode(last_update, p);
  DECODE_FINISH(p);
}

void ClusterLoadMetrics::encode(bufferlist& bl) const {
  ENCODE_START(1, 1, bl);
  encode(rgw_nodes, bl);
  encode(osds, bl);
  encode(cluster_last_update, bl);
  ENCODE_FINISH(bl);
}

void ClusterLoadMetrics::decode(bufferlist::const_iterator& p) {
  DECODE_START(1, p);
  decode(rgw_nodes, p);
  decode(osds, p);
  decode(cluster_last_update, p);
  DECODE_FINISH(p);
}

// RGWClusterLoadMonitor implementation
RGWClusterLoadMonitor::RGWClusterLoadMonitor(CephContext* _cct, rgw::sal::RadosStore* _store)
  : cct(_cct), store(_store) {
  
  // Generate unique node ID
  node_id = cct->_conf->name.to_str() + "." + std::to_string(getpid());
  local_metrics.node_id = node_id;
  
  // Load configuration
  update_config();
  
  ldout(cct, 10) << "RGWClusterLoadMonitor initialized for node " << node_id << dendl;
}

void RGWClusterLoadMonitor::update_config() {
  config.cluster_cpu_threshold_low = cct->_conf.get_val<double>("rgw_cluster_cpu_threshold_low");
  config.cluster_cpu_threshold_high = cct->_conf.get_val<double>("rgw_cluster_cpu_threshold_high");
  config.cluster_memory_threshold_low = cct->_conf.get_val<double>("rgw_cluster_memory_threshold_low");
  config.cluster_memory_threshold_high = cct->_conf.get_val<double>("rgw_cluster_memory_threshold_high");
  
  config.osd_cpu_threshold = cct->_conf.get_val<double>("rgw_osd_cpu_threshold");
  config.osd_disk_threshold = cct->_conf.get_val<double>("rgw_osd_disk_threshold");
  config.osd_max_trim_ops = cct->_conf.get_val<uint64_t>("rgw_osd_max_trim_ops");
  config.osd_latency_threshold_ms = cct->_conf.get_val<double>("rgw_osd_latency_threshold_ms");
  
  config.sleep_ms_low = cct->_conf.get_val<uint64_t>("rgw_cluster_trim_sleep_low");
  config.sleep_ms_medium = cct->_conf.get_val<uint64_t>("rgw_cluster_trim_sleep_medium");
  config.sleep_ms_high = cct->_conf.get_val<uint64_t>("rgw_cluster_trim_sleep_high");
  config.sleep_ms_critical = cct->_conf.get_val<uint64_t>("rgw_cluster_trim_sleep_critical");
  
  config.batch_size_low = cct->_conf.get_val<uint64_t>("rgw_cluster_trim_batch_size_low");
  config.batch_size_medium = cct->_conf.get_val<uint64_t>("rgw_cluster_trim_batch_size_medium");
  config.batch_size_high = cct->_conf.get_val<uint64_t>("rgw_cluster_trim_batch_size_high");
  config.batch_size_critical = cct->_conf.get_val<uint64_t>("rgw_cluster_trim_batch_size_critical");
}

void RGWClusterLoadMonitor::update_local_metrics() {
  // Update CPU load
  std::ifstream loadavg_file("/proc/loadavg");
  if (loadavg_file.is_open()) {
    double load1min;
    loadavg_file >> load1min;
    loadavg_file.close();
    
    long num_cores = sysconf(_SC_NPROCESSORS_ONLN);
    if (num_cores > 0) {
      local_metrics.cpu_load = load1min / num_cores;
    } else {
      local_metrics.cpu_load = load1min;
    }
  }
  
  // Update memory usage
  std::ifstream meminfo_file("/proc/meminfo");
  if (meminfo_file.is_open()) {
    uint64_t mem_total = 0, mem_available = 0;
    std::string line;
    while (std::getline(meminfo_file, line)) {
      if (line.find("MemTotal:") == 0) {
        sscanf(line.c_str(), "MemTotal: %lu kB", &mem_total);
      } else if (line.find("MemAvailable:") == 0) {
        sscanf(line.c_str(), "MemAvailable: %lu kB", &mem_available);
      }
      if (mem_total > 0 && mem_available > 0) {
        break;
      }
    }
    meminfo_file.close();
    
    if (mem_total > 0) {
      local_metrics.memory_usage = (double)(mem_total - mem_available) / mem_total;
    }
  }
  
  // Update active trim operations count
  local_metrics.active_trim_operations = local_trim_operations.load();
  local_metrics.last_update = ceph::real_clock::now();
}

void RGWClusterLoadMonitor::collect_osd_metrics() {
  // Get OSD performance stats from the cluster
  auto rados_svc = store->svc()->rados;
  if (!rados_svc) {
    return;
  }
  
  // Query OSD performance counters
  map<string, string> osd_stats;
  int ret = rados_svc->pool_iterate_begin(RGW_OBJ_NS_NONE, &osd_stats);
  if (ret < 0) {
    ldout(cct, 5) << "failed to get OSD stats: " << cpp_strerror(ret) << dendl;
    return;
  }
  
  // Parse OSD metrics (simplified implementation)
  for (const auto& [osd_name, stats_json] : osd_stats) {
    if (osd_name.find("osd.") == 0) {
      int osd_id = std::stoi(osd_name.substr(4));
      
      ClusterLoadMetrics::OSDMetrics& osd_metrics = cluster_metrics.osds[osd_id];
      osd_metrics.osd_id = osd_id;
      
      // Parse performance counters (simplified)
      // In real implementation, this would parse JSON from OSD perf counters
      osd_metrics.cpu_load = 0.3; // Placeholder
      osd_metrics.disk_usage = 0.6; // Placeholder
      osd_metrics.pending_ops = 50; // Placeholder
      osd_metrics.avg_op_latency_ms = 25.0; // Placeholder
      osd_metrics.last_update = ceph::real_clock::now();
    }
  }
}

rgw_raw_obj RGWClusterLoadMonitor::get_metrics_obj() const {
  rgw_pool pool(store->svc()->zone->get_zone_params().log_pool);
  return rgw_raw_obj(pool, "cluster_load_metrics");
}

void RGWClusterLoadMonitor::publish_local_metrics() {
  // Publish local metrics to cluster storage for other nodes to read
  rgw_raw_obj obj = get_metrics_obj();
  
  // Read existing cluster metrics
  bufferlist bl;
  int ret = store->getRados()->raw_obj_stat(nullptr, obj, nullptr, nullptr, nullptr, nullptr, nullptr, &bl);
  if (ret < 0 && ret != -ENOENT) {
    ldout(cct, 5) << "failed to read cluster metrics: " << cpp_strerror(ret) << dendl;
    return;
  }
  
  ClusterLoadMetrics metrics;
  if (ret == 0 && bl.length() > 0) {
    auto iter = bl.cbegin();
    try {
      metrics.decode(iter);
    } catch (const std::exception& e) {
      ldout(cct, 5) << "failed to decode cluster metrics: " << e.what() << dendl;
      metrics = ClusterLoadMetrics(); // Reset to empty
    }
  }
  
  // Update with local metrics
  metrics.rgw_nodes[node_id] = local_metrics;
  metrics.cluster_last_update = ceph::real_clock::now();
  
  // Clean up old metrics
  auto cutoff = ceph::real_clock::now() - std::chrono::seconds(config.metrics_retention_sec);
  for (auto it = metrics.rgw_nodes.begin(); it != metrics.rgw_nodes.end();) {
    if (it->second.last_update < cutoff) {
      it = metrics.rgw_nodes.erase(it);
    } else {
      ++it;
    }
  }
  
  // Write back to cluster storage
  bl.clear();
  metrics.encode(bl);
  ret = store->getRados()->raw_obj_put(nullptr, obj, bl.length(), bl, {});
  if (ret < 0) {
    ldout(cct, 5) << "failed to publish cluster metrics: " << cpp_strerror(ret) << dendl;
  }
}

void RGWClusterLoadMonitor::update_cluster_metrics() {
  // Read cluster metrics from shared storage
  rgw_raw_obj obj = get_metrics_obj();
  bufferlist bl;
  
  int ret = store->getRados()->raw_obj_stat(nullptr, obj, nullptr, nullptr, nullptr, nullptr, nullptr, &bl);
  if (ret < 0) {
    if (ret != -ENOENT) {
      ldout(cct, 5) << "failed to read cluster metrics: " << cpp_strerror(ret) << dendl;
    }
    return;
  }
  
  if (bl.length() == 0) {
    return;
  }
  
  auto iter = bl.cbegin();
  try {
    std::unique_lock l(lock);
    cluster_metrics.decode(iter);
    last_cluster_update = ceph::real_clock::now();
  } catch (const std::exception& e) {
    ldout(cct, 5) << "failed to decode cluster metrics: " << e.what() << dendl;
  }
}

void RGWClusterLoadMonitor::update_metrics() {
  auto now = ceph::real_clock::now();
  
  // Update local metrics
  if (local_metrics.last_update == ceph::real_clock::zero() ||
      now - local_metrics.last_update >= std::chrono::seconds(config.local_update_interval_sec)) {
    update_local_metrics();
    publish_local_metrics();
  }
  
  // Update cluster metrics
  if (last_cluster_update == ceph::real_clock::zero() ||
      now - last_cluster_update >= std::chrono::seconds(config.cluster_update_interval_sec)) {
    update_cluster_metrics();
    collect_osd_metrics();
  }
}

RGWClusterLoadMonitor::LoadLevel RGWClusterLoadMonitor::get_cluster_load_level() const {
  std::unique_lock l(lock);
  
  if (cluster_metrics.rgw_nodes.empty()) {
    // No cluster data available, use local metrics
    if (local_metrics.cpu_load >= config.cluster_cpu_threshold_high ||
        local_metrics.memory_usage >= config.cluster_memory_threshold_high) {
      return LoadLevel::HIGH;
    } else if (local_metrics.cpu_load >= config.cluster_cpu_threshold_low ||
               local_metrics.memory_usage >= config.cluster_memory_threshold_low) {
      return LoadLevel::MEDIUM;
    }
    return LoadLevel::LOW;
  }
  
  // Calculate cluster-wide averages
  double total_cpu = 0.0, total_memory = 0.0;
  uint64_t total_trim_ops = 0;
  int node_count = 0;
  
  for (const auto& [node_id, metrics] : cluster_metrics.rgw_nodes) {
    total_cpu += metrics.cpu_load;
    total_memory += metrics.memory_usage;
    total_trim_ops += metrics.active_trim_operations;
    node_count++;
  }
  
  if (node_count == 0) {
    return LoadLevel::LOW;
  }
  
  double avg_cpu = total_cpu / node_count;
  double avg_memory = total_memory / node_count;
  
  // Determine load level based on cluster averages
  int high_indicators = 0;
  int medium_indicators = 0;
  
  if (avg_cpu >= config.cluster_cpu_threshold_high) {
    high_indicators++;
  } else if (avg_cpu >= config.cluster_cpu_threshold_low) {
    medium_indicators++;
  }
  
  if (avg_memory >= config.cluster_memory_threshold_high) {
    high_indicators++;
  } else if (avg_memory >= config.cluster_memory_threshold_low) {
    medium_indicators++;
  }
  
  // Consider total trim operations across cluster
  if (total_trim_ops > node_count * 10) { // High trim activity
    high_indicators++;
  } else if (total_trim_ops > node_count * 5) { // Medium trim activity
    medium_indicators++;
  }
  
  if (high_indicators >= 2) {
    return LoadLevel::CRITICAL;
  } else if (high_indicators >= 1) {
    return LoadLevel::HIGH;
  } else if (medium_indicators >= 1) {
    return LoadLevel::MEDIUM;
  }
  
  return LoadLevel::LOW;
}

RGWClusterLoadMonitor::LoadLevel RGWClusterLoadMonitor::get_osd_load_level(const std::string& pool_name) const {
  std::unique_lock l(lock);

  // Get OSDs that would be affected by operations on this pool
  std::vector<int> affected_osds = get_affected_osds(pool_name);

  if (affected_osds.empty()) {
    return LoadLevel::LOW;
  }

  int overloaded_osds = 0;
  int high_load_osds = 0;

  for (int osd_id : affected_osds) {
    auto it = cluster_metrics.osds.find(osd_id);
    if (it == cluster_metrics.osds.end()) {
      continue; // No metrics for this OSD
    }

    const auto& osd_metrics = it->second;

    // Check if OSD is overloaded
    if (osd_metrics.cpu_load >= config.osd_cpu_threshold ||
        osd_metrics.disk_usage >= config.osd_disk_threshold ||
        osd_metrics.trim_ops_in_progress >= config.osd_max_trim_ops ||
        osd_metrics.avg_op_latency_ms >= config.osd_latency_threshold_ms) {
      overloaded_osds++;
    } else if (osd_metrics.cpu_load >= config.osd_cpu_threshold * 0.8 ||
               osd_metrics.disk_usage >= config.osd_disk_threshold * 0.8 ||
               osd_metrics.trim_ops_in_progress >= config.osd_max_trim_ops * 0.8) {
      high_load_osds++;
    }
  }

  // Determine load level based on OSD status
  double overload_ratio = (double)overloaded_osds / affected_osds.size();
  double high_load_ratio = (double)high_load_osds / affected_osds.size();

  if (overload_ratio >= 0.3) { // 30% of OSDs overloaded
    return LoadLevel::CRITICAL;
  } else if (overload_ratio >= 0.1 || high_load_ratio >= 0.5) { // 10% overloaded or 50% high load
    return LoadLevel::HIGH;
  } else if (high_load_ratio >= 0.2) { // 20% high load
    return LoadLevel::MEDIUM;
  }

  return LoadLevel::LOW;
}

RGWClusterLoadMonitor::LoadLevel RGWClusterLoadMonitor::get_overall_load_level(const std::string& pool_name) const {
  LoadLevel cluster_level = get_cluster_load_level();
  LoadLevel osd_level = get_osd_load_level(pool_name);

  // Return the higher of the two load levels
  return std::max(cluster_level, osd_level);
}

std::vector<int> RGWClusterLoadMonitor::get_affected_osds(const std::string& pool_name) const {
  std::vector<int> osds;

  // Get pool information and CRUSH map to determine which OSDs would be affected
  // This is a simplified implementation - in practice, you'd query the CRUSH map
  rgw_pool pool(pool_name);

  // For now, return a subset of known OSDs as a placeholder
  // In real implementation, this would use the CRUSH map to determine
  // which OSDs are used for the given pool
  for (const auto& [osd_id, metrics] : cluster_metrics.osds) {
    osds.push_back(osd_id);
    if (osds.size() >= 10) break; // Limit for testing
  }

  return osds;
}

bool RGWClusterLoadMonitor::is_osd_overloaded(int osd_id) const {
  std::unique_lock l(lock);

  auto it = cluster_metrics.osds.find(osd_id);
  if (it == cluster_metrics.osds.end()) {
    return false; // No metrics available, assume not overloaded
  }

  const auto& metrics = it->second;
  return (metrics.cpu_load >= config.osd_cpu_threshold ||
          metrics.disk_usage >= config.osd_disk_threshold ||
          metrics.trim_ops_in_progress >= config.osd_max_trim_ops ||
          metrics.avg_op_latency_ms >= config.osd_latency_threshold_ms);
}

uint32_t RGWClusterLoadMonitor::get_sleep_time_ms(LoadLevel level) const {
  switch (level) {
    case LoadLevel::CRITICAL:
      return config.sleep_ms_critical;
    case LoadLevel::HIGH:
      return config.sleep_ms_high;
    case LoadLevel::MEDIUM:
      return config.sleep_ms_medium;
    case LoadLevel::LOW:
    default:
      return config.sleep_ms_low;
  }
}

uint32_t RGWClusterLoadMonitor::get_batch_size(LoadLevel level) const {
  switch (level) {
    case LoadLevel::CRITICAL:
      return config.batch_size_critical;
    case LoadLevel::HIGH:
      return config.batch_size_high;
    case LoadLevel::MEDIUM:
      return config.batch_size_medium;
    case LoadLevel::LOW:
    default:
      return config.batch_size_low;
  }
}

bool RGWClusterLoadMonitor::should_throttle(const std::string& pool_name) const {
  return get_overall_load_level(pool_name) != LoadLevel::LOW;
}

bool RGWClusterLoadMonitor::can_start_trim_operation(const std::string& pool_name) const {
  LoadLevel level = get_overall_load_level(pool_name);

  // Don't start new operations if system is critically loaded
  if (level == LoadLevel::CRITICAL) {
    return false;
  }

  // Check if any affected OSDs are overloaded
  std::vector<int> affected_osds = get_affected_osds(pool_name);
  for (int osd_id : affected_osds) {
    if (is_osd_overloaded(osd_id)) {
      return false;
    }
  }

  return true;
}

void RGWClusterLoadMonitor::register_trim_operation(const std::string& pool_name) {
  local_trim_operations++;

  std::unique_lock l(lock);
  pool_trim_operations[pool_name]++;

  ldout(cct, 15) << "registered trim operation for pool " << pool_name
                 << ", total local ops: " << local_trim_operations.load() << dendl;
}

void RGWClusterLoadMonitor::unregister_trim_operation(const std::string& pool_name) {
  if (local_trim_operations > 0) {
    local_trim_operations--;
  }

  std::unique_lock l(lock);
  if (pool_trim_operations[pool_name] > 0) {
    pool_trim_operations[pool_name]--;
  }

  ldout(cct, 15) << "unregistered trim operation for pool " << pool_name
                 << ", total local ops: " << local_trim_operations.load() << dendl;
}

void RGWClusterLoadMonitor::dump_cluster_metrics(const DoutPrefixProvider *dpp) const {
  std::unique_lock l(lock);

  ldpp_dout(dpp, 10) << "RGWClusterLoadMonitor cluster metrics:" << dendl;
  ldpp_dout(dpp, 10) << "  RGW nodes: " << cluster_metrics.rgw_nodes.size() << dendl;

  for (const auto& [node_id, metrics] : cluster_metrics.rgw_nodes) {
    ldpp_dout(dpp, 10) << "    " << node_id
                       << ": cpu=" << metrics.cpu_load
                       << " mem=" << metrics.memory_usage
                       << " trim_ops=" << metrics.active_trim_operations << dendl;
  }

  ldpp_dout(dpp, 10) << "  OSDs: " << cluster_metrics.osds.size() << dendl;
  for (const auto& [osd_id, metrics] : cluster_metrics.osds) {
    ldpp_dout(dpp, 10) << "    osd." << osd_id
                       << ": cpu=" << metrics.cpu_load
                       << " disk=" << metrics.disk_usage
                       << " pending=" << metrics.pending_ops
                       << " trim_ops=" << metrics.trim_ops_in_progress
                       << " latency=" << metrics.avg_op_latency_ms << "ms" << dendl;
  }

  LoadLevel cluster_level = get_cluster_load_level();
  ldpp_dout(dpp, 10) << "  Overall cluster load level: " << static_cast<int>(cluster_level) << dendl;
}

void RGWClusterLoadMonitor::dump_osd_metrics(const DoutPrefixProvider *dpp, const std::string& pool_name) const {
  std::vector<int> affected_osds = get_affected_osds(pool_name);
  LoadLevel osd_level = get_osd_load_level(pool_name);

  ldpp_dout(dpp, 10) << "OSD metrics for pool " << pool_name << ":" << dendl;
  ldpp_dout(dpp, 10) << "  Affected OSDs: " << affected_osds.size() << dendl;
  ldpp_dout(dpp, 10) << "  OSD load level: " << static_cast<int>(osd_level) << dendl;

  for (int osd_id : affected_osds) {
    auto it = cluster_metrics.osds.find(osd_id);
    if (it != cluster_metrics.osds.end()) {
      const auto& metrics = it->second;
      bool overloaded = is_osd_overloaded(osd_id);
      ldpp_dout(dpp, 10) << "    osd." << osd_id
                         << ": cpu=" << metrics.cpu_load
                         << " disk=" << metrics.disk_usage
                         << " trim_ops=" << metrics.trim_ops_in_progress
                         << " latency=" << metrics.avg_op_latency_ms << "ms"
                         << (overloaded ? " [OVERLOADED]" : "") << dendl;
    }
  }
}

std::unique_ptr<RGWClusterLoadMonitor> create_cluster_load_monitor(
  CephContext* cct, rgw::sal::RadosStore* store) {
  return std::make_unique<RGWClusterLoadMonitor>(cct, store);
}

} // namespace rgw
