// -*- mode:C++; tab-width:8; c-basic-offset:2; indent-tabs-mode:t -*-
// vim: ts=8 sw=2 smarttab ft=cpp

/*
 * Ceph - scalable distributed file system
 *
 * Copyright (C) 2024 Red Hat, Inc
 *
 * This is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License version 2.1, as published by the Free Software
 * Foundation.  See file COPYING.
 */

#ifndef RGW_CLUSTER_LOAD_MONITOR_H
#define RGW_CLUSTER_LOAD_MONITOR_H

#include <memory>
#include <map>
#include <vector>
#include <atomic>
#include <mutex>
#include <chrono>

#include "include/common_fwd.h"
#include "common/ceph_mutex.h"
#include "common/ceph_time.h"
#include "rgw_sal.h"

// Forward declarations
class RGWRados;
class DoutPrefixProvider;

namespace rgw {

/// Cluster-wide load metrics
struct ClusterLoadMetrics {
  // RGW nodes metrics
  struct RGWNodeMetrics {
    std::string node_id;
    double cpu_load = 0.0;
    double memory_usage = 0.0;
    uint64_t active_trim_operations = 0;
    ceph::real_time last_update = ceph::real_clock::zero();
    
    void encode(bufferlist& bl) const;
    void decode(bufferlist::const_iterator& p);
  };
  
  // OSD metrics
  struct OSDMetrics {
    int osd_id = -1;
    double cpu_load = 0.0;
    double disk_usage = 0.0;
    uint64_t pending_ops = 0;
    uint64_t trim_ops_in_progress = 0;
    double avg_op_latency_ms = 0.0;
    ceph::real_time last_update = ceph::real_clock::zero();
    
    void encode(bufferlist& bl) const;
    void decode(bufferlist::const_iterator& p);
  };
  
  std::map<std::string, RGWNodeMetrics> rgw_nodes;
  std::map<int, OSDMetrics> osds;
  ceph::real_time cluster_last_update = ceph::real_clock::zero();
  
  void encode(bufferlist& bl) const;
  void decode(bufferlist::const_iterator& p);
};

/// Cluster-aware load monitor for RGW trim operations
class RGWClusterLoadMonitor {
private:
  CephContext* cct;
  rgw::sal::RadosStore* store;
  mutable ceph::mutex lock = ceph::make_mutex("RGWClusterLoadMonitor::lock");
  
  // Local node metrics
  std::string node_id;
  ClusterLoadMetrics::RGWNodeMetrics local_metrics;
  
  // Cluster-wide metrics cache
  ClusterLoadMetrics cluster_metrics;
  ceph::real_time last_cluster_update = ceph::real_clock::zero();
  
  // Configuration
  struct Config {
    // Cluster thresholds
    double cluster_cpu_threshold_low = 0.4;
    double cluster_cpu_threshold_high = 0.7;
    double cluster_memory_threshold_low = 0.5;
    double cluster_memory_threshold_high = 0.8;
    
    // OSD protection thresholds
    double osd_cpu_threshold = 0.8;
    double osd_disk_threshold = 0.9;
    uint64_t osd_max_trim_ops = 10;
    double osd_latency_threshold_ms = 100.0;
    
    // Update intervals
    uint32_t local_update_interval_sec = 5;
    uint32_t cluster_update_interval_sec = 30;
    uint32_t metrics_retention_sec = 300;
    
    // Throttling parameters
    uint32_t sleep_ms_low = 10;
    uint32_t sleep_ms_medium = 50;
    uint32_t sleep_ms_high = 200;
    uint32_t sleep_ms_critical = 500;
    
    uint32_t batch_size_low = 1000;
    uint32_t batch_size_medium = 500;
    uint32_t batch_size_high = 100;
    uint32_t batch_size_critical = 10;
  } config;
  
  // Internal methods
  void update_local_metrics();
  void update_cluster_metrics();
  void publish_local_metrics();
  void collect_osd_metrics();
  bool is_osd_overloaded(int osd_id) const;
  std::vector<int> get_affected_osds(const std::string& pool_name) const;
  
public:
  explicit RGWClusterLoadMonitor(CephContext* _cct, rgw::sal::RadosStore* _store);
  ~RGWClusterLoadMonitor() = default;
  
  // Load level enumeration
  enum class LoadLevel {
    LOW = 0,
    MEDIUM = 1,
    HIGH = 2,
    CRITICAL = 3
  };
  
  // Main interface
  void update_metrics();
  LoadLevel get_cluster_load_level() const;
  LoadLevel get_osd_load_level(const std::string& pool_name) const;
  LoadLevel get_overall_load_level(const std::string& pool_name) const;
  
  // Throttling parameters
  uint32_t get_sleep_time_ms(LoadLevel level) const;
  uint32_t get_batch_size(LoadLevel level) const;
  bool should_throttle(const std::string& pool_name) const;
  bool can_start_trim_operation(const std::string& pool_name) const;
  
  // Coordination
  void register_trim_operation(const std::string& pool_name);
  void unregister_trim_operation(const std::string& pool_name);
  
  // Monitoring and debugging
  void dump_cluster_metrics(const DoutPrefixProvider *dpp) const;
  void dump_osd_metrics(const DoutPrefixProvider *dpp, const std::string& pool_name) const;
  
  // Configuration
  void update_config();
  
private:
  // Metrics storage object for cluster coordination
  rgw_raw_obj get_metrics_obj() const;
  
  // Active trim operations tracking
  std::atomic<uint64_t> local_trim_operations{0};
  std::map<std::string, uint64_t> pool_trim_operations;
};

/// Factory function
std::unique_ptr<RGWClusterLoadMonitor> create_cluster_load_monitor(
  CephContext* cct, rgw::sal::RadosStore* store);

} // namespace rgw

WRITE_CLASS_ENCODER(rgw::ClusterLoadMetrics::RGWNodeMetrics);
WRITE_CLASS_ENCODER(rgw::ClusterLoadMetrics::OSDMetrics);
WRITE_CLASS_ENCODER(rgw::ClusterLoadMetrics);

#endif // RGW_CLUSTER_LOAD_MONITOR_H
