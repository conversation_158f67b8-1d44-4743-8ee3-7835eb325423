[tox]
envlist = py3, mypy, lint
skip_missing_interpreters = true

[testenv:py3]
deps=
    -rrequirements.txt
    -c{toxinidir}/../mypy-constrains.txt
commands=
    pytest --doctest-modules ceph/deployment/service_spec.py ceph/utils.py
    pytest {posargs}
    mypy --config-file=../mypy.ini -p ceph

[testenv:mypy]
deps=
    -rrequirements.txt
    -c{toxinidir}/../mypy-constrains.txt
commands=
    mypy --config-file=../mypy.ini -p ceph

[tool:pytest]
norecursedirs = .* _* virtualenv

[flake8]
max-line-length = 100
exclude =
    __pycache__

[testenv:lint]
deps = 
    -rrequirements-lint.txt
commands =
    flake8 {posargs:ceph}
    rstcheck --report info --debug README.rst

