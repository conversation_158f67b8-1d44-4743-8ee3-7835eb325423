// 简化的动态限速测试程序
#include <iostream>
#include <fstream>
#include <string>
#include <unistd.h>
#include <thread>
#include <chrono>
#include <memory>

// 简化的负载监控器类
class SimpleLoadMonitor {
private:
  // 负载指标
  double cpu_load_avg = 0.0;
  double memory_usage_percent = 0.0;
  
  // 配置参数
  struct Config {
    double cpu_threshold_low = 0.5;
    double cpu_threshold_high = 0.8;
    double mem_threshold_low = 0.6;
    double mem_threshold_high = 0.85;
    
    uint32_t sleep_ms_low = 10;
    uint32_t sleep_ms_medium = 50;
    uint32_t sleep_ms_high = 200;
    
    uint32_t batch_size_low = 1000;
    uint32_t batch_size_medium = 500;
    uint32_t batch_size_high = 100;
  } config;
  
  // 获取 CPU 负载
  double get_cpu_load() const {
    std::ifstream loadavg_file("/proc/loadavg");
    if (!loadavg_file.is_open()) {
      return 0.0;
    }
    
    double load1min;
    loadavg_file >> load1min;
    loadavg_file.close();
    
    // 按 CPU 核心数归一化
    long num_cores = sysconf(_SC_NPROCESSORS_ONLN);
    if (num_cores > 0) {
      return load1min / num_cores;
    }
    return load1min;
  }
  
  // 获取内存使用率
  double get_memory_usage() const {
    std::ifstream meminfo_file("/proc/meminfo");
    if (!meminfo_file.is_open()) {
      return 0.0;
    }
    
    uint64_t mem_total = 0, mem_available = 0;
    std::string line;
    while (std::getline(meminfo_file, line)) {
      if (line.find("MemTotal:") == 0) {
        sscanf(line.c_str(), "MemTotal: %lu kB", &mem_total);
      } else if (line.find("MemAvailable:") == 0) {
        sscanf(line.c_str(), "MemAvailable: %lu kB", &mem_available);
      }
      if (mem_total > 0 && mem_available > 0) {
        break;
      }
    }
    meminfo_file.close();
    
    if (mem_total > 0) {
      return (double)(mem_total - mem_available) / mem_total;
    }
    return 0.0;
  }

public:
  enum class LoadLevel {
    LOW = 0,
    MEDIUM = 1,
    HIGH = 2
  };
  
  void update_load_metrics() {
    cpu_load_avg = get_cpu_load();
    memory_usage_percent = get_memory_usage();
  }
  
  LoadLevel get_current_load_level() const {
    int high_indicators = 0;
    int medium_indicators = 0;
    
    // 检查 CPU 负载
    if (cpu_load_avg >= config.cpu_threshold_high) {
      high_indicators++;
    } else if (cpu_load_avg >= config.cpu_threshold_low) {
      medium_indicators++;
    }
    
    // 检查内存使用
    if (memory_usage_percent >= config.mem_threshold_high) {
      high_indicators++;
    } else if (memory_usage_percent >= config.mem_threshold_low) {
      medium_indicators++;
    }
    
    // 确定整体负载级别
    if (high_indicators >= 1) {
      return LoadLevel::HIGH;
    } else if (medium_indicators >= 1) {
      return LoadLevel::MEDIUM;
    }
    return LoadLevel::LOW;
  }
  
  uint32_t get_sleep_time_ms() const {
    switch (get_current_load_level()) {
      case LoadLevel::HIGH:
        return config.sleep_ms_high;
      case LoadLevel::MEDIUM:
        return config.sleep_ms_medium;
      case LoadLevel::LOW:
      default:
        return config.sleep_ms_low;
    }
  }
  
  uint32_t get_batch_size() const {
    switch (get_current_load_level()) {
      case LoadLevel::HIGH:
        return config.batch_size_high;
      case LoadLevel::MEDIUM:
        return config.batch_size_medium;
      case LoadLevel::LOW:
      default:
        return config.batch_size_low;
    }
  }
  
  bool should_throttle() const {
    return get_current_load_level() != LoadLevel::LOW;
  }
  
  void dump_metrics() const {
    std::cout << "负载指标: "
              << "CPU=" << cpu_load_avg
              << " 内存=" << memory_usage_percent
              << " 负载级别=" << static_cast<int>(get_current_load_level())
              << " 休眠时间=" << get_sleep_time_ms() << "ms"
              << " 批次大小=" << get_batch_size()
              << std::endl;
  }
  
  // 设置配置用于测试
  void set_config(double cpu_low, double cpu_high, double mem_low, double mem_high) {
    config.cpu_threshold_low = cpu_low;
    config.cpu_threshold_high = cpu_high;
    config.mem_threshold_low = mem_low;
    config.mem_threshold_high = mem_high;
  }
};

// 模拟 trim 操作
class MockTrimOperation {
private:
  SimpleLoadMonitor* load_monitor;
  int operations_count = 0;
  
public:
  MockTrimOperation(SimpleLoadMonitor* monitor) : load_monitor(monitor) {}
  
  int trim_with_throttling(int total_operations) {
    std::cout << "\n开始执行 " << total_operations << " 个 trim 操作..." << std::endl;
    
    load_monitor->update_load_metrics();
    load_monitor->dump_metrics();
    
    const uint32_t batch_size = load_monitor->get_batch_size();
    const uint32_t sleep_time_ms = load_monitor->get_sleep_time_ms();
    
    std::cout << "使用批次大小: " << batch_size 
              << ", 休眠时间: " << sleep_time_ms << "ms" << std::endl;
    
    for (int i = 0; i < total_operations; ++i) {
      operations_count++;
      
      // 模拟操作
      std::this_thread::sleep_for(std::chrono::milliseconds(1));
      
      // 应用限速
      if (load_monitor->should_throttle() && 
          operations_count % batch_size == 0) {
        std::cout << "批次 " << (operations_count / batch_size) 
                  << " 完成，休眠 " << sleep_time_ms << "ms..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time_ms));
        
        // 定期更新负载指标
        load_monitor->update_load_metrics();
      }
    }
    
    std::cout << "完成 " << operations_count << " 个操作" << std::endl;
    return 0;
  }
};

// 测试函数
void test_load_monitoring() {
  std::cout << "=== 测试负载监控 ===" << std::endl;
  
  SimpleLoadMonitor monitor;
  monitor.update_load_metrics();
  monitor.dump_metrics();
  
  std::cout << "当前是否需要限速: " << (monitor.should_throttle() ? "是" : "否") << std::endl;
}

void test_different_scenarios() {
  std::cout << "\n=== 测试不同场景配置 ===" << std::endl;
  
  SimpleLoadMonitor monitor;
  
  // 高性能场景
  std::cout << "\n高性能场景配置:" << std::endl;
  monitor.set_config(0.7, 0.9, 0.7, 0.9);
  monitor.update_load_metrics();
  monitor.dump_metrics();
  
  // 资源受限场景
  std::cout << "\n资源受限场景配置:" << std::endl;
  monitor.set_config(0.3, 0.6, 0.5, 0.75);
  monitor.update_load_metrics();
  monitor.dump_metrics();
  
  // 延迟敏感场景
  std::cout << "\n延迟敏感场景配置:" << std::endl;
  monitor.set_config(0.4, 0.7, 0.55, 0.8);
  monitor.update_load_metrics();
  monitor.dump_metrics();
}

void test_throttled_operations() {
  std::cout << "\n=== 测试限速操作 ===" << std::endl;
  
  SimpleLoadMonitor monitor;
  MockTrimOperation trim_op(&monitor);
  
  // 执行一些 trim 操作
  trim_op.trim_with_throttling(50);
}

int main() {
  std::cout << "RGW 动态限速测试程序" << std::endl;
  std::cout << "===================" << std::endl;
  
  try {
    // 测试负载监控
    test_load_monitoring();
    
    // 测试不同场景
    test_different_scenarios();
    
    // 测试限速操作
    test_throttled_operations();
    
    std::cout << "\n所有测试完成!" << std::endl;
    return 0;
    
  } catch (const std::exception& e) {
    std::cerr << "测试失败: " << e.what() << std::endl;
    return 1;
  }
}
