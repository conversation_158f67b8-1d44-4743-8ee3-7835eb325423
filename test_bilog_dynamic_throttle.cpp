// Bucket Index Log 动态限速测试程序
#include <iostream>
#include <iomanip>
#include <fstream>
#include <string>
#include <unistd.h>
#include <thread>
#include <chrono>
#include <memory>
#include <vector>
#include <atomic>

// 简化的 bucket index log 负载监控器类
class SimpleBucketIndexLogLoadMonitor {
private:
  // 模拟的负载指标
  std::atomic<double> simulated_cpu_load{0.0};
  std::atomic<double> simulated_memory_usage{0.0};
  
  // 配置参数
  struct Config {
    double cpu_threshold_low = 0.5;
    double cpu_threshold_high = 0.8;
    double mem_threshold_low = 0.6;
    double mem_threshold_high = 0.85;
    
    uint32_t sleep_ms_low = 10;
    uint32_t sleep_ms_medium = 50;
    uint32_t sleep_ms_high = 200;
    
    uint32_t batch_size_low = 16;     // For bucket instances
    uint32_t batch_size_medium = 8;   // For bucket instances
    uint32_t batch_size_high = 4;     // For bucket instances
  } config;
  
  // 获取真实 CPU 负载
  double get_real_cpu_load() const {
    std::ifstream loadavg_file("/proc/loadavg");
    if (!loadavg_file.is_open()) {
      return 0.0;
    }
    
    double load1min;
    loadavg_file >> load1min;
    loadavg_file.close();
    
    long num_cores = sysconf(_SC_NPROCESSORS_ONLN);
    if (num_cores > 0) {
      return load1min / num_cores;
    }
    return load1min;
  }
  
  // 获取真实内存使用率
  double get_real_memory_usage() const {
    std::ifstream meminfo_file("/proc/meminfo");
    if (!meminfo_file.is_open()) {
      return 0.0;
    }
    
    uint64_t mem_total = 0, mem_available = 0;
    std::string line;
    while (std::getline(meminfo_file, line)) {
      if (line.find("MemTotal:") == 0) {
        sscanf(line.c_str(), "MemTotal: %lu kB", &mem_total);
      } else if (line.find("MemAvailable:") == 0) {
        sscanf(line.c_str(), "MemAvailable: %lu kB", &mem_available);
      }
      if (mem_total > 0 && mem_available > 0) {
        break;
      }
    }
    meminfo_file.close();
    
    if (mem_total > 0) {
      return (double)(mem_total - mem_available) / mem_total;
    }
    return 0.0;
  }

public:
  enum class LoadLevel {
    LOW = 0,
    MEDIUM = 1,
    HIGH = 2
  };
  
  // 设置模拟负载
  void set_simulated_load(double cpu, double memory) {
    simulated_cpu_load = cpu;
    simulated_memory_usage = memory;
  }
  
  // 使用模拟负载或真实负载
  double get_cpu_load(bool use_simulation = false) const {
    if (use_simulation) {
      return simulated_cpu_load.load();
    }
    return get_real_cpu_load();
  }
  
  double get_memory_usage(bool use_simulation = false) const {
    if (use_simulation) {
      return simulated_memory_usage.load();
    }
    return get_real_memory_usage();
  }
  
  LoadLevel get_current_load_level(bool use_simulation = false) const {
    double cpu = get_cpu_load(use_simulation);
    double memory = get_memory_usage(use_simulation);
    
    int high_indicators = 0;
    int medium_indicators = 0;
    
    // 检查 CPU 负载
    if (cpu >= config.cpu_threshold_high) {
      high_indicators++;
    } else if (cpu >= config.cpu_threshold_low) {
      medium_indicators++;
    }
    
    // 检查内存使用
    if (memory >= config.mem_threshold_high) {
      high_indicators++;
    } else if (memory >= config.mem_threshold_low) {
      medium_indicators++;
    }
    
    // 确定整体负载级别
    if (high_indicators >= 1) {
      return LoadLevel::HIGH;
    } else if (medium_indicators >= 1) {
      return LoadLevel::MEDIUM;
    }
    return LoadLevel::LOW;
  }
  
  uint32_t get_sleep_time_ms(bool use_simulation = false) const {
    switch (get_current_load_level(use_simulation)) {
      case LoadLevel::HIGH:
        return config.sleep_ms_high;
      case LoadLevel::MEDIUM:
        return config.sleep_ms_medium;
      case LoadLevel::LOW:
      default:
        return config.sleep_ms_low;
    }
  }
  
  uint32_t get_batch_size(bool use_simulation = false) const {
    switch (get_current_load_level(use_simulation)) {
      case LoadLevel::HIGH:
        return config.batch_size_high;
      case LoadLevel::MEDIUM:
        return config.batch_size_medium;
      case LoadLevel::LOW:
      default:
        return config.batch_size_low;
    }
  }
  
  bool should_throttle(bool use_simulation = false) const {
    return get_current_load_level(use_simulation) != LoadLevel::LOW;
  }
  
  void dump_metrics(bool use_simulation = false) const {
    double cpu = get_cpu_load(use_simulation);
    double memory = get_memory_usage(use_simulation);
    auto level = get_current_load_level(use_simulation);
    
    std::string level_str;
    switch (level) {
      case LoadLevel::LOW: level_str = "低"; break;
      case LoadLevel::MEDIUM: level_str = "中"; break;
      case LoadLevel::HIGH: level_str = "高"; break;
    }
    
    std::cout << "Bucket Index Log 负载指标: "
              << "CPU=" << std::fixed << std::setprecision(3) << cpu
              << " 内存=" << std::fixed << std::setprecision(3) << memory
              << " 负载级别=" << level_str
              << " 休眠时间=" << get_sleep_time_ms(use_simulation) << "ms"
              << " 批次大小=" << get_batch_size(use_simulation)
              << std::endl;
  }
};

// 模拟 bucket index log trim 操作
class MockBucketIndexLogTrimOperation {
private:
  SimpleBucketIndexLogLoadMonitor* load_monitor;
  int bucket_operations_count = 0;
  int shard_operations_count = 0;
  
public:
  MockBucketIndexLogTrimOperation(SimpleBucketIndexLogLoadMonitor* monitor) 
    : load_monitor(monitor) {}
  
  struct Stats {
    int total_buckets = 0;
    int total_shards = 0;
    std::chrono::milliseconds total_time{0};
    std::chrono::milliseconds total_sleep_time{0};
    int bucket_throttle_events = 0;
    int shard_throttle_events = 0;
    double buckets_per_second = 0.0;
    double shards_per_second = 0.0;
  };
  
  Stats trim_with_throttling(int total_buckets, int shards_per_bucket, bool use_simulation = false) {
    std::cout << "\n开始执行 " << total_buckets << " 个 bucket 的 index log trim 操作..." << std::endl;
    std::cout << "每个 bucket 有 " << shards_per_bucket << " 个分片" << std::endl;
    
    auto start_time = std::chrono::steady_clock::now();
    bucket_operations_count = 0;
    shard_operations_count = 0;
    
    load_monitor->dump_metrics(use_simulation);
    
    const uint32_t bucket_batch_size = load_monitor->get_batch_size(use_simulation);
    const uint32_t sleep_time_ms = load_monitor->get_sleep_time_ms(use_simulation);
    
    std::cout << "使用 bucket 批次大小: " << bucket_batch_size 
              << ", 休眠时间: " << sleep_time_ms << "ms" << std::endl;
    
    auto total_sleep_time = std::chrono::milliseconds(0);
    int bucket_throttle_events = 0;
    int shard_throttle_events = 0;
    
    // 模拟处理每个 bucket
    for (int bucket_idx = 0; bucket_idx < total_buckets; ++bucket_idx) {
      bucket_operations_count++;
      
      // Bucket 级别的限速
      if (load_monitor->should_throttle(use_simulation) && 
          bucket_operations_count % bucket_batch_size == 0) {
        std::cout << "Bucket 批次 " << (bucket_operations_count / bucket_batch_size) 
                  << " 完成，休眠 " << sleep_time_ms << "ms..." << std::endl;
        
        auto sleep_start = std::chrono::steady_clock::now();
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time_ms));
        auto sleep_end = std::chrono::steady_clock::now();
        
        auto actual_sleep = std::chrono::duration_cast<std::chrono::milliseconds>(sleep_end - sleep_start);
        total_sleep_time += actual_sleep;
        bucket_throttle_events++;
      }
      
      // 模拟处理每个分片
      for (int shard_idx = 0; shard_idx < shards_per_bucket; ++shard_idx) {
        shard_operations_count++;
        
        // 模拟分片操作（更短的时间）
        std::this_thread::sleep_for(std::chrono::microseconds(50));
        
        // 分片级别的限速（使用更小的批次大小）
        const uint32_t shard_batch_size = bucket_batch_size * 2; // 分片批次大小更大
        if (load_monitor->should_throttle(use_simulation) && 
            shard_operations_count % shard_batch_size == 0) {
          std::cout << "  分片批次 " << (shard_operations_count / shard_batch_size) 
                    << " 完成，休眠 " << (sleep_time_ms / 2) << "ms..." << std::endl;
          
          auto sleep_start = std::chrono::steady_clock::now();
          std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time_ms / 2));
          auto sleep_end = std::chrono::steady_clock::now();
          
          auto actual_sleep = std::chrono::duration_cast<std::chrono::milliseconds>(sleep_end - sleep_start);
          total_sleep_time += actual_sleep;
          shard_throttle_events++;
        }
      }
    }
    
    auto end_time = std::chrono::steady_clock::now();
    auto total_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    Stats stats;
    stats.total_buckets = bucket_operations_count;
    stats.total_shards = shard_operations_count;
    stats.total_time = total_time;
    stats.total_sleep_time = total_sleep_time;
    stats.bucket_throttle_events = bucket_throttle_events;
    stats.shard_throttle_events = shard_throttle_events;
    stats.buckets_per_second = (double)bucket_operations_count / (total_time.count() / 1000.0);
    stats.shards_per_second = (double)shard_operations_count / (total_time.count() / 1000.0);
    
    std::cout << "完成 " << bucket_operations_count << " 个 bucket，" 
              << shard_operations_count << " 个分片操作" << std::endl;
    return stats;
  }
};

void print_bilog_stats(const MockBucketIndexLogTrimOperation::Stats& stats, const std::string& scenario) {
  std::cout << "\n=== " << scenario << " 统计 ===" << std::endl;
  std::cout << "总 bucket 数: " << stats.total_buckets << std::endl;
  std::cout << "总分片数: " << stats.total_shards << std::endl;
  std::cout << "总耗时: " << stats.total_time.count() << "ms" << std::endl;
  std::cout << "限速休眠时间: " << stats.total_sleep_time.count() << "ms" << std::endl;
  std::cout << "Bucket 限速事件数: " << stats.bucket_throttle_events << std::endl;
  std::cout << "分片限速事件数: " << stats.shard_throttle_events << std::endl;
  std::cout << "Bucket 处理速率: " << std::fixed << std::setprecision(2) << stats.buckets_per_second << " buckets/sec" << std::endl;
  std::cout << "分片处理速率: " << std::fixed << std::setprecision(2) << stats.shards_per_second << " shards/sec" << std::endl;
  std::cout << "限速开销: " << std::fixed << std::setprecision(1) 
            << (double)stats.total_sleep_time.count() / stats.total_time.count() * 100 << "%" << std::endl;
}

int main() {
  std::cout << "RGW Bucket Index Log 动态限速测试" << std::endl;
  std::cout << "=================================" << std::endl;
  
  SimpleBucketIndexLogLoadMonitor monitor;
  MockBucketIndexLogTrimOperation trim_op(&monitor);
  
  const int test_buckets = 50;
  const int shards_per_bucket = 8;
  
  // 测试 1: 低负载场景
  std::cout << "\n测试 1: 低负载场景" << std::endl;
  monitor.set_simulated_load(0.2, 0.3);  // 低 CPU 和内存使用
  auto stats_low = trim_op.trim_with_throttling(test_buckets, shards_per_bucket, true);
  print_bilog_stats(stats_low, "低负载场景");
  
  // 测试 2: 中等负载场景
  std::cout << "\n测试 2: 中等负载场景" << std::endl;
  monitor.set_simulated_load(0.6, 0.7);  // 中等 CPU 和内存使用
  auto stats_medium = trim_op.trim_with_throttling(test_buckets, shards_per_bucket, true);
  print_bilog_stats(stats_medium, "中等负载场景");
  
  // 测试 3: 高负载场景
  std::cout << "\n测试 3: 高负载场景" << std::endl;
  monitor.set_simulated_load(0.9, 0.9);  // 高 CPU 和内存使用
  auto stats_high = trim_op.trim_with_throttling(test_buckets, shards_per_bucket, true);
  print_bilog_stats(stats_high, "高负载场景");
  
  // 测试 4: 真实系统负载
  std::cout << "\n测试 4: 真实系统负载" << std::endl;
  auto stats_real = trim_op.trim_with_throttling(test_buckets, shards_per_bucket, false);
  print_bilog_stats(stats_real, "真实系统负载");
  
  // 性能对比
  std::cout << "\n=== 性能对比 ===" << std::endl;
  std::cout << "低负载 vs 高负载 bucket 处理速率比: " 
            << std::fixed << std::setprecision(2) 
            << stats_low.buckets_per_second / stats_high.buckets_per_second << ":1" << std::endl;
  std::cout << "低负载 vs 高负载分片处理速率比: " 
            << std::fixed << std::setprecision(2) 
            << stats_low.shards_per_second / stats_high.shards_per_second << ":1" << std::endl;
  std::cout << "高负载限速开销: " 
            << std::fixed << std::setprecision(1)
            << (double)stats_high.total_sleep_time.count() / stats_high.total_time.count() * 100 
            << "%" << std::endl;
  
  std::cout << "\n所有测试完成!" << std::endl;
  return 0;
}
