# RGW 动态限速机制

## 概述

Ceph RGW 的动态限速机制为 mdlog、datalog 和 bucket index log 的 trim 操作提供了基于系统负载的自适应限速功能。该机制可以根据 CPU 负载、内存使用率、RADOS 操作延迟等指标动态调整 trim 操作的频率和批次大小，避免在系统高负载时对性能造成过大影响。

## 功能特性

### 1. 多维度负载监控
- **CPU 负载监控**: 监控系统 1 分钟平均负载，按 CPU 核心数归一化
- **内存使用监控**: 监控系统内存使用百分比
- **RADOS 延迟监控**: 监控 RADOS 操作延迟
- **待处理操作监控**: 监控待处理的操作数量

### 2. 三级限速策略
- **低负载 (LOW)**: 正常速度执行 trim 操作
- **中等负载 (MEDIUM)**: 适度限速，增加操作间隔
- **高负载 (HIGH)**: 强力限速，显著减少操作频率

### 3. 自适应参数调整
- **批次大小**: 根据负载动态调整每批处理的操作数量
- **休眠时间**: 根据负载动态调整操作间的休眠时间
- **实时监控**: 定期更新负载指标，实时调整限速策略

## 配置选项

### 主开关
```bash
# 启用动态限速 (默认: false)
rgw_datalog_trim_dynamic_throttle = true
```

### CPU 负载阈值
```bash
# CPU 低负载阈值 (默认: 0.5)
rgw_datalog_trim_cpu_threshold_low = 0.5

# CPU 高负载阈值 (默认: 0.8)  
rgw_datalog_trim_cpu_threshold_high = 0.8
```

### 内存使用阈值
```bash
# 内存低负载阈值 (默认: 0.6)
rgw_datalog_trim_mem_threshold_low = 0.6

# 内存高负载阈值 (默认: 0.85)
rgw_datalog_trim_mem_threshold_high = 0.85
```

### 限速参数
```bash
# 低负载休眠时间 (毫秒, 默认: 10)
rgw_datalog_trim_sleep_low = 10

# 中等负载休眠时间 (毫秒, 默认: 50)
rgw_datalog_trim_sleep_medium = 50

# 高负载休眠时间 (毫秒, 默认: 200)
rgw_datalog_trim_sleep_high = 200

# 低负载批次大小 (默认: 1000)
rgw_datalog_trim_batch_size_low = 1000

# 中等负载批次大小 (默认: 500)
rgw_datalog_trim_batch_size_medium = 500

# 高负载批次大小 (默认: 100)
rgw_datalog_trim_batch_size_high = 100
```

## 使用方法

### 1. 启用动态限速
```bash
# 在 ceph.conf 中添加配置
[client.rgw]
rgw_datalog_trim_dynamic_throttle = true

# 或者运行时设置
ceph config set client.rgw rgw_datalog_trim_dynamic_throttle true
```

### 2. 调整负载阈值
根据您的系统特性调整负载阈值：

```bash
# 对于高性能系统，可以提高阈值
ceph config set client.rgw rgw_datalog_trim_cpu_threshold_low 0.7
ceph config set client.rgw rgw_datalog_trim_cpu_threshold_high 0.9

# 对于资源受限系统，可以降低阈值
ceph config set client.rgw rgw_datalog_trim_cpu_threshold_low 0.3
ceph config set client.rgw rgw_datalog_trim_cpu_threshold_high 0.6
```

### 3. 调整限速强度
根据业务需求调整限速强度：

```bash
# 更激进的限速 (适用于对延迟敏感的业务)
ceph config set client.rgw rgw_datalog_trim_sleep_high 500
ceph config set client.rgw rgw_datalog_trim_batch_size_high 50

# 更温和的限速 (适用于对 trim 效率要求较高的场景)
ceph config set client.rgw rgw_datalog_trim_sleep_high 100
ceph config set client.rgw rgw_datalog_trim_batch_size_high 200
```

## 监控和调优

### 1. 查看负载指标
动态限速会在日志中输出负载指标：

```bash
# 查看 RGW 日志中的负载信息
grep "RGWDataLogLoadMonitor metrics" /var/log/ceph/ceph-client.rgw.*.log
```

### 2. 监控限速效果
```bash
# 查看限速操作日志
grep "trim_entries_throttled" /var/log/ceph/ceph-client.rgw.*.log

# 查看批次处理信息
grep "throttling after" /var/log/ceph/ceph-client.rgw.*.log
```

### 3. 性能调优建议

#### 高 I/O 负载场景
```bash
# 降低 CPU 阈值，更早触发限速
rgw_datalog_trim_cpu_threshold_low = 0.4
rgw_datalog_trim_cpu_threshold_high = 0.7

# 增加休眠时间，减少 I/O 压力
rgw_datalog_trim_sleep_medium = 100
rgw_datalog_trim_sleep_high = 300
```

#### 内存敏感场景
```bash
# 降低内存阈值
rgw_datalog_trim_mem_threshold_low = 0.5
rgw_datalog_trim_mem_threshold_high = 0.75

# 减小批次大小，降低内存使用
rgw_datalog_trim_batch_size_medium = 300
rgw_datalog_trim_batch_size_high = 50
```

#### 延迟敏感场景
```bash
# 更激进的限速策略
rgw_datalog_trim_sleep_low = 20
rgw_datalog_trim_sleep_medium = 100
rgw_datalog_trim_sleep_high = 500

# 更小的批次大小
rgw_datalog_trim_batch_size_low = 500
rgw_datalog_trim_batch_size_medium = 200
rgw_datalog_trim_batch_size_high = 50
```

## 故障排除

### 1. 动态限速未生效
检查配置是否正确启用：
```bash
ceph config show client.rgw | grep rgw_datalog_trim_dynamic_throttle
```

### 2. 限速过于激进
如果 trim 操作过慢，可以：
- 提高负载阈值
- 减少休眠时间
- 增加批次大小

### 3. 限速不够
如果系统仍然受到 trim 操作影响，可以：
- 降低负载阈值
- 增加休眠时间
- 减少批次大小

## 扩展到其他日志类型

该动态限速机制的设计可以扩展到 mdlog 和 bucket index log：

### MDLog 扩展
在 `rgw_trim_mdlog.cc` 中应用类似的限速逻辑：
```cpp
// 在 MetaPeerTrimShardCR::operate() 中添加限速
if (load_monitor && load_monitor->should_throttle()) {
  std::this_thread::sleep_for(
    std::chrono::milliseconds(load_monitor->get_sleep_time_ms()));
}
```

### Bucket Index Log 扩展
在 `rgw_trim_bilog.cc` 中的 `BucketTrimShardCollectCR` 中应用限速：
```cpp
// 在批次处理之间添加限速检查
if (operations_count % batch_size == 0 && load_monitor->should_throttle()) {
  std::this_thread::sleep_for(
    std::chrono::milliseconds(load_monitor->get_sleep_time_ms()));
}
```

## 总结

RGW 动态限速机制提供了一个智能的、自适应的解决方案来管理日志 trim 操作对系统性能的影响。通过合理配置和监控，可以在保证日志清理效率的同时，最大限度地减少对业务性能的影响。
