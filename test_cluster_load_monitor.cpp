// 集群负载监控测试程序
#include <iostream>
#include <iomanip>
#include <fstream>
#include <string>
#include <unistd.h>
#include <thread>
#include <chrono>
#include <memory>
#include <vector>
#include <map>
#include <atomic>

// 简化的集群负载监控器类
class SimpleClusterLoadMonitor {
private:
  // 模拟的集群指标
  struct RGWNodeMetrics {
    std::string node_id;
    double cpu_load = 0.0;
    double memory_usage = 0.0;
    uint64_t active_trim_operations = 0;
  };
  
  struct OSDMetrics {
    int osd_id = -1;
    double cpu_load = 0.0;
    double disk_usage = 0.0;
    uint64_t pending_ops = 0;
    uint64_t trim_ops_in_progress = 0;
    double avg_op_latency_ms = 0.0;
  };
  
  std::map<std::string, RGWNodeMetrics> rgw_nodes;
  std::map<int, OSDMetrics> osds;
  
  // 配置参数
  struct Config {
    double cluster_cpu_threshold_low = 0.4;
    double cluster_cpu_threshold_high = 0.7;
    double cluster_memory_threshold_low = 0.5;
    double cluster_memory_threshold_high = 0.8;
    
    double osd_cpu_threshold = 0.8;
    double osd_disk_threshold = 0.9;
    uint64_t osd_max_trim_ops = 10;
    double osd_latency_threshold_ms = 100.0;
    
    uint32_t sleep_ms_low = 10;
    uint32_t sleep_ms_medium = 50;
    uint32_t sleep_ms_high = 200;
    uint32_t sleep_ms_critical = 500;
    
    uint32_t batch_size_low = 1000;
    uint32_t batch_size_medium = 500;
    uint32_t batch_size_high = 100;
    uint32_t batch_size_critical = 10;
  } config;

public:
  enum class LoadLevel {
    LOW = 0,
    MEDIUM = 1,
    HIGH = 2,
    CRITICAL = 3
  };
  
  // 模拟集群数据
  void simulate_cluster_data() {
    // 模拟 3 个 RGW 节点
    rgw_nodes["rgw.node1"] = {"rgw.node1", 0.3, 0.4, 2};
    rgw_nodes["rgw.node2"] = {"rgw.node2", 0.5, 0.6, 3};
    rgw_nodes["rgw.node3"] = {"rgw.node3", 0.2, 0.3, 1};
    
    // 模拟 6 个 OSD
    osds[0] = {0, 0.4, 0.7, 20, 3, 45.0};
    osds[1] = {1, 0.6, 0.8, 35, 5, 65.0};
    osds[2] = {2, 0.3, 0.6, 15, 2, 30.0};
    osds[3] = {3, 0.8, 0.9, 50, 8, 120.0}; // 过载的 OSD
    osds[4] = {4, 0.5, 0.7, 25, 4, 55.0};
    osds[5] = {5, 0.2, 0.5, 10, 1, 25.0};
  }
  
  void simulate_high_load_cluster() {
    // 模拟高负载集群
    rgw_nodes["rgw.node1"] = {"rgw.node1", 0.8, 0.9, 10};
    rgw_nodes["rgw.node2"] = {"rgw.node2", 0.9, 0.85, 12};
    rgw_nodes["rgw.node3"] = {"rgw.node3", 0.7, 0.8, 8};
    
    // 模拟高负载 OSD
    osds[0] = {0, 0.9, 0.95, 80, 12, 150.0}; // 严重过载
    osds[1] = {1, 0.85, 0.9, 70, 11, 130.0}; // 过载
    osds[2] = {2, 0.8, 0.85, 60, 9, 110.0};  // 高负载
    osds[3] = {3, 0.95, 0.98, 100, 15, 200.0}; // 严重过载
    osds[4] = {4, 0.7, 0.8, 45, 7, 90.0};
    osds[5] = {5, 0.6, 0.7, 35, 5, 70.0};
  }
  
  LoadLevel get_cluster_load_level() const {
    if (rgw_nodes.empty()) {
      return LoadLevel::LOW;
    }
    
    // 计算集群平均值
    double total_cpu = 0.0, total_memory = 0.0;
    uint64_t total_trim_ops = 0;
    
    for (const auto& [node_id, metrics] : rgw_nodes) {
      total_cpu += metrics.cpu_load;
      total_memory += metrics.memory_usage;
      total_trim_ops += metrics.active_trim_operations;
    }
    
    double avg_cpu = total_cpu / rgw_nodes.size();
    double avg_memory = total_memory / rgw_nodes.size();
    
    int high_indicators = 0;
    int medium_indicators = 0;
    
    if (avg_cpu >= config.cluster_cpu_threshold_high) {
      high_indicators++;
    } else if (avg_cpu >= config.cluster_cpu_threshold_low) {
      medium_indicators++;
    }
    
    if (avg_memory >= config.cluster_memory_threshold_high) {
      high_indicators++;
    } else if (avg_memory >= config.cluster_memory_threshold_low) {
      medium_indicators++;
    }
    
    // 考虑总的 trim 操作数
    if (total_trim_ops > rgw_nodes.size() * 10) {
      high_indicators++;
    } else if (total_trim_ops > rgw_nodes.size() * 5) {
      medium_indicators++;
    }
    
    if (high_indicators >= 2) {
      return LoadLevel::CRITICAL;
    } else if (high_indicators >= 1) {
      return LoadLevel::HIGH;
    } else if (medium_indicators >= 1) {
      return LoadLevel::MEDIUM;
    }
    
    return LoadLevel::LOW;
  }
  
  LoadLevel get_osd_load_level(const std::string& pool_name) const {
    // 模拟获取受影响的 OSD（简化实现）
    std::vector<int> affected_osds = {0, 1, 2, 3}; // 假设这些 OSD 受影响
    
    int overloaded_osds = 0;
    int high_load_osds = 0;
    
    for (int osd_id : affected_osds) {
      auto it = osds.find(osd_id);
      if (it == osds.end()) {
        continue;
      }
      
      const auto& osd_metrics = it->second;
      
      // 检查 OSD 是否过载
      if (osd_metrics.cpu_load >= config.osd_cpu_threshold ||
          osd_metrics.disk_usage >= config.osd_disk_threshold ||
          osd_metrics.trim_ops_in_progress >= config.osd_max_trim_ops ||
          osd_metrics.avg_op_latency_ms >= config.osd_latency_threshold_ms) {
        overloaded_osds++;
      } else if (osd_metrics.cpu_load >= config.osd_cpu_threshold * 0.8 ||
                 osd_metrics.disk_usage >= config.osd_disk_threshold * 0.8 ||
                 osd_metrics.trim_ops_in_progress >= config.osd_max_trim_ops * 0.8) {
        high_load_osds++;
      }
    }
    
    double overload_ratio = (double)overloaded_osds / affected_osds.size();
    double high_load_ratio = (double)high_load_osds / affected_osds.size();
    
    if (overload_ratio >= 0.3) {
      return LoadLevel::CRITICAL;
    } else if (overload_ratio >= 0.1 || high_load_ratio >= 0.5) {
      return LoadLevel::HIGH;
    } else if (high_load_ratio >= 0.2) {
      return LoadLevel::MEDIUM;
    }
    
    return LoadLevel::LOW;
  }
  
  LoadLevel get_overall_load_level(const std::string& pool_name) const {
    LoadLevel cluster_level = get_cluster_load_level();
    LoadLevel osd_level = get_osd_load_level(pool_name);
    return std::max(cluster_level, osd_level);
  }
  
  uint32_t get_sleep_time_ms(LoadLevel level) const {
    switch (level) {
      case LoadLevel::CRITICAL: return config.sleep_ms_critical;
      case LoadLevel::HIGH: return config.sleep_ms_high;
      case LoadLevel::MEDIUM: return config.sleep_ms_medium;
      case LoadLevel::LOW:
      default: return config.sleep_ms_low;
    }
  }
  
  uint32_t get_batch_size(LoadLevel level) const {
    switch (level) {
      case LoadLevel::CRITICAL: return config.batch_size_critical;
      case LoadLevel::HIGH: return config.batch_size_high;
      case LoadLevel::MEDIUM: return config.batch_size_medium;
      case LoadLevel::LOW:
      default: return config.batch_size_low;
    }
  }
  
  bool can_start_trim_operation(const std::string& pool_name) const {
    LoadLevel level = get_overall_load_level(pool_name);
    return level != LoadLevel::CRITICAL;
  }
  
  void dump_cluster_metrics() const {
    std::cout << "\n=== 集群负载指标 ===" << std::endl;
    std::cout << "RGW 节点数: " << rgw_nodes.size() << std::endl;
    
    double total_cpu = 0.0, total_memory = 0.0;
    uint64_t total_trim_ops = 0;
    
    for (const auto& [node_id, metrics] : rgw_nodes) {
      std::cout << "  " << node_id 
                << ": CPU=" << std::fixed << std::setprecision(3) << metrics.cpu_load
                << " 内存=" << std::fixed << std::setprecision(3) << metrics.memory_usage
                << " trim操作=" << metrics.active_trim_operations << std::endl;
      total_cpu += metrics.cpu_load;
      total_memory += metrics.memory_usage;
      total_trim_ops += metrics.active_trim_operations;
    }
    
    std::cout << "集群平均: CPU=" << std::fixed << std::setprecision(3) << (total_cpu / rgw_nodes.size())
              << " 内存=" << std::fixed << std::setprecision(3) << (total_memory / rgw_nodes.size())
              << " 总trim操作=" << total_trim_ops << std::endl;
  }
  
  void dump_osd_metrics(const std::string& pool_name) const {
    std::cout << "\n=== OSD 负载指标 (Pool: " << pool_name << ") ===" << std::endl;
    std::cout << "OSD 数量: " << osds.size() << std::endl;
    
    int overloaded_count = 0;
    for (const auto& [osd_id, metrics] : osds) {
      bool overloaded = (metrics.cpu_load >= config.osd_cpu_threshold ||
                        metrics.disk_usage >= config.osd_disk_threshold ||
                        metrics.trim_ops_in_progress >= config.osd_max_trim_ops ||
                        metrics.avg_op_latency_ms >= config.osd_latency_threshold_ms);
      
      if (overloaded) overloaded_count++;
      
      std::cout << "  osd." << osd_id
                << ": CPU=" << std::fixed << std::setprecision(3) << metrics.cpu_load
                << " 磁盘=" << std::fixed << std::setprecision(3) << metrics.disk_usage
                << " 待处理=" << metrics.pending_ops
                << " trim操作=" << metrics.trim_ops_in_progress
                << " 延迟=" << std::fixed << std::setprecision(1) << metrics.avg_op_latency_ms << "ms"
                << (overloaded ? " [过载]" : "") << std::endl;
    }
    
    std::cout << "过载 OSD 数量: " << overloaded_count << "/" << osds.size() << std::endl;
  }
  
  std::string load_level_to_string(LoadLevel level) const {
    switch (level) {
      case LoadLevel::LOW: return "低";
      case LoadLevel::MEDIUM: return "中";
      case LoadLevel::HIGH: return "高";
      case LoadLevel::CRITICAL: return "严重";
      default: return "未知";
    }
  }
};

// 模拟集群感知的 trim 操作
class ClusterAwareTrimOperation {
private:
  SimpleClusterLoadMonitor* monitor;
  int operations_count = 0;
  
public:
  ClusterAwareTrimOperation(SimpleClusterLoadMonitor* m) : monitor(m) {}
  
  struct Stats {
    int total_operations = 0;
    std::chrono::milliseconds total_time{0};
    std::chrono::milliseconds total_sleep_time{0};
    int throttle_events = 0;
    int rejected_operations = 0;
    double ops_per_second = 0.0;
  };
  
  Stats trim_with_cluster_awareness(const std::string& pool_name, int total_operations) {
    std::cout << "\n开始执行集群感知的 trim 操作..." << std::endl;
    std::cout << "Pool: " << pool_name << ", 操作数: " << total_operations << std::endl;
    
    auto start_time = std::chrono::steady_clock::now();
    operations_count = 0;
    
    monitor->dump_cluster_metrics();
    monitor->dump_osd_metrics(pool_name);
    
    auto cluster_level = monitor->get_cluster_load_level();
    auto osd_level = monitor->get_osd_load_level(pool_name);
    auto overall_level = monitor->get_overall_load_level(pool_name);
    
    std::cout << "\n负载级别评估:" << std::endl;
    std::cout << "  集群负载: " << monitor->load_level_to_string(cluster_level) << std::endl;
    std::cout << "  OSD负载: " << monitor->load_level_to_string(osd_level) << std::endl;
    std::cout << "  整体负载: " << monitor->load_level_to_string(overall_level) << std::endl;
    
    const uint32_t batch_size = monitor->get_batch_size(overall_level);
    const uint32_t sleep_time_ms = monitor->get_sleep_time_ms(overall_level);
    
    std::cout << "使用参数: 批次大小=" << batch_size 
              << ", 休眠时间=" << sleep_time_ms << "ms" << std::endl;
    
    auto total_sleep_time = std::chrono::milliseconds(0);
    int throttle_events = 0;
    int rejected_operations = 0;
    
    for (int i = 0; i < total_operations; ++i) {
      // 检查是否可以开始新操作
      if (!monitor->can_start_trim_operation(pool_name)) {
        std::cout << "操作 " << (i + 1) << " 被拒绝 - 系统负载过高" << std::endl;
        rejected_operations++;
        continue;
      }
      
      operations_count++;
      
      // 模拟操作
      std::this_thread::sleep_for(std::chrono::microseconds(100));
      
      // 应用集群感知的限速
      if (overall_level != SimpleClusterLoadMonitor::LoadLevel::LOW && 
          operations_count % batch_size == 0) {
        std::cout << "批次 " << (operations_count / batch_size) 
                  << " 完成，集群感知休眠 " << sleep_time_ms << "ms..." << std::endl;
        
        auto sleep_start = std::chrono::steady_clock::now();
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time_ms));
        auto sleep_end = std::chrono::steady_clock::now();
        
        auto actual_sleep = std::chrono::duration_cast<std::chrono::milliseconds>(sleep_end - sleep_start);
        total_sleep_time += actual_sleep;
        throttle_events++;
      }
    }
    
    auto end_time = std::chrono::steady_clock::now();
    auto total_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    Stats stats;
    stats.total_operations = operations_count;
    stats.total_time = total_time;
    stats.total_sleep_time = total_sleep_time;
    stats.throttle_events = throttle_events;
    stats.rejected_operations = rejected_operations;
    stats.ops_per_second = (double)operations_count / (total_time.count() / 1000.0);
    
    std::cout << "完成 " << operations_count << " 个操作，拒绝 " << rejected_operations << " 个操作" << std::endl;
    return stats;
  }
};

void print_cluster_stats(const ClusterAwareTrimOperation::Stats& stats, const std::string& scenario) {
  std::cout << "\n=== " << scenario << " 统计 ===" << std::endl;
  std::cout << "成功操作数: " << stats.total_operations << std::endl;
  std::cout << "拒绝操作数: " << stats.rejected_operations << std::endl;
  std::cout << "总耗时: " << stats.total_time.count() << "ms" << std::endl;
  std::cout << "限速休眠时间: " << stats.total_sleep_time.count() << "ms" << std::endl;
  std::cout << "限速事件数: " << stats.throttle_events << std::endl;
  std::cout << "操作速率: " << std::fixed << std::setprecision(2) << stats.ops_per_second << " ops/sec" << std::endl;
  std::cout << "限速开销: " << std::fixed << std::setprecision(1) 
            << (stats.total_time.count() > 0 ? (double)stats.total_sleep_time.count() / stats.total_time.count() * 100 : 0) << "%" << std::endl;
}

int main() {
  std::cout << "RGW 集群感知动态限速测试" << std::endl;
  std::cout << "========================" << std::endl;
  
  SimpleClusterLoadMonitor monitor;
  ClusterAwareTrimOperation trim_op(&monitor);
  
  const int test_operations = 200;
  const std::string pool_name = "default.rgw.log";
  
  // 测试 1: 正常负载集群
  std::cout << "\n测试 1: 正常负载集群" << std::endl;
  monitor.simulate_cluster_data();
  auto stats_normal = trim_op.trim_with_cluster_awareness(pool_name, test_operations);
  print_cluster_stats(stats_normal, "正常负载集群");
  
  // 测试 2: 高负载集群
  std::cout << "\n测试 2: 高负载集群" << std::endl;
  monitor.simulate_high_load_cluster();
  auto stats_high = trim_op.trim_with_cluster_awareness(pool_name, test_operations);
  print_cluster_stats(stats_high, "高负载集群");
  
  // 性能对比
  std::cout << "\n=== 性能对比 ===" << std::endl;
  std::cout << "正常负载 vs 高负载速率比: " 
            << std::fixed << std::setprecision(2) 
            << (stats_high.ops_per_second > 0 ? stats_normal.ops_per_second / stats_high.ops_per_second : 0) << ":1" << std::endl;
  std::cout << "高负载拒绝率: " 
            << std::fixed << std::setprecision(1)
            << (double)stats_high.rejected_operations / test_operations * 100 << "%" << std::endl;
  
  std::cout << "\n所有测试完成!" << std::endl;
  return 0;
}
